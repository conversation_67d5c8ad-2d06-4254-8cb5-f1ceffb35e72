package ai.showlab.bff.common.aspect;

import ai.showlab.bff.common.aop.CustomCacheAspect;
import ai.showlab.bff.entity.domain.v1.member.Member;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CustomCacheAspect 类型转换测试
 * 
 * <AUTHOR>
 * @date 2024-08-01
 */
@ExtendWith(MockitoExtension.class)
class CustomCacheAspectTest {

    @InjectMocks
    private CustomCacheAspect customCacheAspect;

    private Member testMember;
    private JSONObject memberJsonObject;

    @BeforeEach
    void setUp() {
        // 创建测试用的Member对象
        testMember = new Member();
        testMember.setId(1L);
        testMember.setUsername("testuser");
        testMember.setNickname("Test User");
        testMember.setEmail("<EMAIL>");
        testMember.setPhone("13800138000");
        testMember.setAvatar("avatar.jpg");
        testMember.setCreateTime(OffsetDateTime.now());
        testMember.setUpdateTime(OffsetDateTime.now());

        // 创建对应的JSONObject
        memberJsonObject = (JSONObject) JSON.toJSON(testMember);
    }

    @Test
    void testConvertCachedValue_JSONObjectToMember() throws Exception {
        // 获取测试方法
        Method method = TestService.class.getMethod("getMember", Long.class);
        
        // 调用类型转换方法
        Object result = invokeConvertCachedValue(memberJsonObject, method);
        
        // 验证结果
        assertNotNull(result);
        assertInstanceOf(Member.class, result);
        
        Member convertedMember = (Member) result;
        assertEquals(testMember.getId(), convertedMember.getId());
        assertEquals(testMember.getUsername(), convertedMember.getUsername());
        assertEquals(testMember.getNickname(), convertedMember.getNickname());
        assertEquals(testMember.getEmail(), convertedMember.getEmail());
        assertEquals(testMember.getPhone(), convertedMember.getPhone());
    }

    @Test
    void testConvertCachedValue_ListOfJSONObjectToListOfMember() throws Exception {
        // 创建JSONObject列表
        List<JSONObject> jsonObjectList = Arrays.asList(memberJsonObject, memberJsonObject);
        
        // 获取测试方法
        Method method = TestService.class.getMethod("getMemberList");
        
        // 调用类型转换方法
        Object result = invokeConvertCachedValue(jsonObjectList, method);
        
        // 验证结果
        assertNotNull(result);
        assertInstanceOf(List.class, result);
        
        @SuppressWarnings("unchecked")
        List<Member> convertedList = (List<Member>) result;
        assertEquals(2, convertedList.size());
        
        Member firstMember = convertedList.get(0);
        assertEquals(testMember.getId(), firstMember.getId());
        assertEquals(testMember.getUsername(), firstMember.getUsername());
    }

    @Test
    void testConvertCachedValue_StringValue() throws Exception {
        // 获取测试方法
        Method method = TestService.class.getMethod("getString");
        
        String testString = "test string";
        
        // 调用类型转换方法
        Object result = invokeConvertCachedValue(testString, method);
        
        // 验证结果
        assertNotNull(result);
        assertInstanceOf(String.class, result);
        assertEquals(testString, result);
    }

    @Test
    void testConvertCachedValue_NullValue() throws Exception {
        // 获取测试方法
        Method method = TestService.class.getMethod("getMember", Long.class);
        
        // 调用类型转换方法
        Object result = invokeConvertCachedValue(null, method);
        
        // 验证结果
        assertNull(result);
    }

    @Test
    void testConvertCachedValue_AlreadyCorrectType() throws Exception {
        // 获取测试方法
        Method method = TestService.class.getMethod("getMember", Long.class);
        
        // 调用类型转换方法（传入已经是正确类型的对象）
        Object result = invokeConvertCachedValue(testMember, method);
        
        // 验证结果
        assertNotNull(result);
        assertInstanceOf(Member.class, result);
        assertSame(testMember, result); // 应该是同一个对象
    }

    /**
     * 通过反射调用私有方法 convertCachedValue
     */
    private Object invokeConvertCachedValue(Object cachedValue, Method method) throws Exception {
        Method convertMethod = CustomCacheAspect.class.getDeclaredMethod("convertCachedValue", Object.class, Method.class);
        convertMethod.setAccessible(true);
        return convertMethod.invoke(customCacheAspect, cachedValue, method);
    }

    /**
     * 测试服务类，用于提供测试方法
     */
    public static class TestService {
        public Member getMember(Long id) {
            return null;
        }

        public List<Member> getMemberList() {
            return null;
        }

        public String getString() {
            return null;
        }
    }
}
