package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.entity.domain.v1.member.MemberSession;
import ai.showlab.bff.service.v1.member.IDuplicateLoginService;
import ai.showlab.bff.service.v1.member.IMemberSessionService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 重复登录服务测试类
 * 
 * <AUTHOR>
 * @date 2024-07-26
 */
@ExtendWith(MockitoExtension.class)
class DuplicateLoginServiceImplTest {

    @InjectMocks
    private DuplicateLoginServiceImpl duplicateLoginService;

    @Mock
    private IMemberSessionService memberSessionService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @Mock
    private HttpServletRequest request;

    private Long memberId;
    private List<MemberSession> mockSessions;

    @BeforeEach
    void setUp() {
        memberId = 1001L;
        
        // 设置配置值
        ReflectionTestUtils.setField(duplicateLoginService, "duplicateLoginStrategy", "KICK_OLD");
        ReflectionTestUtils.setField(duplicateLoginService, "maxConcurrentSessions", 3);
        ReflectionTestUtils.setField(duplicateLoginService, "checkSameDevice", false);
        
        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        
        // 模拟请求信息
        when(request.getHeader("User-Agent")).thenReturn("Mozilla/5.0 Test Browser");
        
        // 创建模拟会话数据
        mockSessions = createMockSessions();
    }

    @Test
    void testCheckAndHandleDuplicateLogin_FirstLogin() {
        // 模拟首次登录（无活跃会话）
        when(memberSessionService.getActiveSessionsForMember(memberId)).thenReturn(new ArrayList<>());
        
        IDuplicateLoginService.DuplicateLoginResult result = 
                duplicateLoginService.checkAndHandleDuplicateLogin(memberId, request);
        
        assertTrue(result.isAllowed());
        assertEquals("首次登录，允许", result.getMessage());
        assertNull(result.getKickedSessions());
    }

    @Test
    void testCheckAndHandleDuplicateLogin_WithinLimit() {
        // 模拟会话数量未超限（2个活跃会话，限制为3个）
        List<MemberSession> twoSessions = mockSessions.subList(0, 2);
        when(memberSessionService.getActiveSessionsForMember(memberId)).thenReturn(twoSessions);
        
        IDuplicateLoginService.DuplicateLoginResult result = 
                duplicateLoginService.checkAndHandleDuplicateLogin(memberId, request);
        
        assertTrue(result.isAllowed());
        assertEquals("会话数量未超限，允许登录", result.getMessage());
    }

    @Test
    void testCheckAndHandleDuplicateLogin_KickOldStrategy() {
        // 模拟会话数量超限，使用KICK_OLD策略
        when(memberSessionService.getActiveSessionsForMember(memberId))
                .thenReturn(mockSessions) // 第一次调用返回所有会话
                .thenReturn(mockSessions); // 第二次调用（在kickOldestSessionsAndReturn中）也返回所有会话
        
        IDuplicateLoginService.DuplicateLoginResult result = 
                duplicateLoginService.checkAndHandleDuplicateLogin(memberId, request);
        
        assertTrue(result.isAllowed());
        assertTrue(result.getMessage().contains("已踢掉"));
        assertNotNull(result.getKickedSessions());
        
        // 验证调用了terminateSession方法
        verify(memberSessionService, atLeastOnce()).terminateSession(eq(memberId), anyLong());
    }

    @Test
    void testGetActiveSessionCount() {
        when(memberSessionService.getActiveSessionsForMember(memberId)).thenReturn(mockSessions);
        
        int count = duplicateLoginService.getActiveSessionCount(memberId);
        
        assertEquals(mockSessions.size(), count);
    }

    @Test
    void testKickOldestSessions() {
        when(memberSessionService.getActiveSessionsForMember(memberId)).thenReturn(mockSessions);
        
        duplicateLoginService.kickOldestSessions(memberId, 2);
        
        // 验证踢掉了最旧的会话（应该踢掉1个会话，保留2个）
        verify(memberSessionService, times(1)).terminateSession(eq(memberId), anyLong());
    }

    /**
     * 创建模拟会话数据
     */
    private List<MemberSession> createMockSessions() {
        List<MemberSession> sessions = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            MemberSession session = new MemberSession();
            session.setId((long) i);
            session.setMemberId(memberId);
            session.setToken("token_" + i);
            session.setDeviceType(1);
            session.setDeviceInfo("Device " + i);
            session.setIpAddress("192.168.1." + i);
            session.setLoginTime(OffsetDateTime.now().minusHours(i)); // 不同的登录时间
            session.setExpireTime(OffsetDateTime.now().plusDays(30));
            session.setIsActive(true);
            sessions.add(session);
        }
        
        return sessions;
    }
}
