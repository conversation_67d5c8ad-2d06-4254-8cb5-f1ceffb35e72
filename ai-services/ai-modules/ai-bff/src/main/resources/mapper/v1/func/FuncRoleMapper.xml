<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.func.FuncRoleMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.func.FuncRole" id="FuncRoleResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="description"    column="description"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFuncRoleVo">
        select id, pid, code, name, sort_order, description, delete_time, create_time, update_time
        from a_func_role
    </sql>

    <select id="selectRolesByMemberId" parameterType="Long" resultMap="FuncRoleResult">
        select distinct r.id, r.pid, r.code, r.name, r.sort_order, r.description, r.delete_time, r.create_time, r.update_time
        from a_func_role r
        inner join a_member_role mr on r.id = mr.role_id
        where mr.member_id = #{memberId}
          and r.delete_time is null
          and mr.delete_time is null
          and r.status = 1
        order by r.sort_order
    </select>

    <select id="selectRoleById" parameterType="Long" resultMap="FuncRoleResult">
        <include refid="selectFuncRoleVo"/>
        where id = #{id}
          and delete_time is null
          and status = 1
    </select>

    <select id="selectRoleByCode" parameterType="String" resultMap="FuncRoleResult">
        <include refid="selectFuncRoleVo"/>
        where code = #{code}
          and delete_time is null
          and status = 1
    </select>

    <select id="selectAllRoles" resultMap="FuncRoleResult">
        <include refid="selectFuncRoleVo"/>
        where delete_time is null and status = 1
        order by sort_order
    </select>

    <select id="checkMemberHasRole" resultType="java.lang.Integer">
        select count(1) from a_member_role mr
        inner join a_func_role r on mr.role_id = r.id
        where mr.member_id = #{memberId}
          and r.code = #{roleCode}
          and mr.delete_time is null
          and r.delete_time is null
          and r.status = 1
    </select>

    <select id="selectRolesByPid" parameterType="Long" resultMap="FuncRoleResult">
        <include refid="selectFuncRoleVo"/>
        where pid = #{pid}
          and delete_time is null
          and status = 1
        order by sort_order
    </select>
</mapper> 