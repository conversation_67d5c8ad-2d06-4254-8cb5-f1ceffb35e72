<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.member.MemberSessionMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.member.MemberSession" id="MemberSessionResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="token" column="token"/>
        <result property="deviceType" column="device_type"/>
        <result property="deviceInfo" column="device_info"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="loginTime" column="login_time"/>
        <result property="expireTime" column="expire_time"/>
        <result property="isActive" column="is_active"/>
    </resultMap>

    <!-- 面向外部用户展示的会话信息 -->
    <sql id="selectMemberSessionInfoVo">
        select id, device_type, device_info, ip_address, login_time, expire_time, is_active
        from a_member_session
    </sql>

    <sql id="selectInternalMemberSessionVo">
        select id, member_id, token, device_type, device_info, ip_address, login_time, expire_time, is_active
        from a_member_session
    </sql>

    <select id="selectMemberSessionByToken" parameterType="String" resultMap="MemberSessionResult">
        <include refid="selectInternalMemberSessionVo"/>
        where token = #{token}
          and is_active = true
          and expire_time > now()
          and delete_time is null
    </select>

    <select id="selectMemberSessionsByMemberId" parameterType="Long" resultMap="MemberSessionResult">
        <include refid="selectMemberSessionInfoVo"/>
        where member_id = #{memberId}
          and is_active = true
          and delete_time is null
        order by login_time desc
    </select>

    <select id="selectMemberSessionById" parameterType="Long" resultMap="MemberSessionResult">
        <include refid="selectInternalMemberSessionVo"/>
        where id = #{id} and delete_time is null
    </select>

    <insert id="insertMemberSession" parameterType="ai.showlab.bff.entity.domain.v1.member.MemberSession" useGeneratedKeys="true" keyProperty="id">
        insert into a_member_session (member_id, token, device_type, device_info, ip_address,
                                      login_time, expire_time, is_active, create_time, update_time)
        values (#{memberId}, #{token}, #{deviceType}, #{deviceInfo}, #{ipAddress},
                #{loginTime}, #{expireTime}, #{isActive}, now(), now())
    </insert>

    <update id="deactivateMemberSession" parameterType="String">
        update a_member_session
        set is_active = false,
            update_time = now()
        where token = #{token} and delete_time is null
    </update>

    <update id="deactivateAllMemberSessions" parameterType="Long">
        update a_member_session
        set is_active = false,
            update_time = now()
        where member_id = #{memberId} and is_active = true and delete_time is null
    </update>
</mapper> 