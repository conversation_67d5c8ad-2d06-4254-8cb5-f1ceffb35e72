<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.knowledge.KnowledgeDocChunkMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.knowledge.KnowledgeDocChunk" id="KnowledgeDocChunkResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="content" column="content"/>
        <result property="charCount" column="char_count"/>
        <result property="metadata" column="metadata"/>
    </resultMap>

    <sql id="selectKnowledgeDocChunkVo">
        select id, doc_id, content, char_count, metadata
        from a_knowledge_doc_chunk
    </sql>

    <select id="selectChunksByDocId" parameterType="Long" resultMap="KnowledgeDocChunkResult">
        <include refid="selectKnowledgeDocChunkVo"/>
        where doc_id = #{docId} and delete_time is null
    </select>
    
    <select id="selectChunkById" parameterType="Long" resultMap="KnowledgeDocChunkResult">
        <include refid="selectKnowledgeDocChunkVo"/>
        where id = #{id} and delete_time is null
    </select>

    <insert id="batchInsertChunks">
        insert into a_knowledge_doc_chunk (doc_id, content, char_count, metadata, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.docId}, #{item.content}, #{item.charCount}, #{item.metadata}, now(), now())
        </foreach>
    </insert>

    <update id="softDeleteChunksByDocId" parameterType="Long">
        update a_knowledge_doc_chunk
        set delete_time = now()
        where doc_id = #{docId}
    </update>

</mapper> 