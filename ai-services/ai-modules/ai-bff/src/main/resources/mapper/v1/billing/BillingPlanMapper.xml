<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingPlanMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingPlan" id="BillingPlanResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="unit" column="unit"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="description" column="description"/>
    </resultMap>

    <sql id="selectBillingPlanVo">
        select id, name, unit, sort_order, description
        from a_billing_plan
    </sql>

    <select id="selectAllPlans" resultMap="BillingPlanResult">
        <include refid="selectBillingPlanVo"/>
        where delete_time is null
        order by sort_order asc
    </select>

    <select id="selectPlanById" parameterType="Long" resultMap="BillingPlanResult">
        <include refid="selectBillingPlanVo"/>
        where id = #{id} and delete_time is null
    </select>

</mapper> 