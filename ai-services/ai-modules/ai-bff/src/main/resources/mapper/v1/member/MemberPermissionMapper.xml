<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.member.MemberPermissionMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.member.MemberPermission" id="MemberPermissionResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="permissionId" column="permission_id"/>
        <result property="isGranted" column="is_granted"/>
        <result property="scope" column="scope"/>
        <result property="validFrom" column="valid_from"/>
        <result property="validTo" column="valid_to"/>
    </resultMap>

    <select id="selectPermissionIdsByMemberId" parameterType="Long" resultType="java.lang.Long">
        select permission_id from a_member_permission
        where member_id = #{memberId}
          and is_granted = true
          and delete_time is null
          and (valid_from is null or valid_from &lt;= now())
          and (valid_to is null or valid_to &gt;= now())
    </select>

    <select id="checkMemberHasPermission" resultType="java.lang.Integer">
        select count(1) from a_member_permission
        where member_id = #{memberId}
          and permission_id = #{permissionId}
          and is_granted = true
          and delete_time is null
          and (valid_from is null or valid_from &lt;= now())
          and (valid_to is null or valid_to &gt;= now())
    </select>

    <insert id="insertMemberPermission" parameterType="ai.showlab.bff.entity.domain.v1.member.MemberPermission" useGeneratedKeys="true" keyProperty="id">
        insert into a_member_permission (member_id, permission_id, is_granted, scope, valid_from, valid_to, create_time, update_time)
        values (#{memberId}, #{permissionId}, #{isGranted}, #{scope}, #{validFrom}, #{validTo}, now(), now())
    </insert>

    <update id="updateMemberPermission" parameterType="ai.showlab.bff.entity.domain.v1.member.MemberPermission">
        update a_member_permission
        <trim prefix="SET" suffixOverrides=",">
            <if test="isGranted != null">is_granted = #{isGranted},</if>
            <if test="scope != null and scope != ''">scope = #{scope},</if>
            <if test="validFrom != null">valid_from = #{validFrom},</if>
            <if test="validTo != null">valid_to = #{validTo},</if>
            update_time = now(),
        </trim>
        where id = #{id} and delete_time is null
    </update>

    <update id="softDeleteMemberPermission" parameterType="Long">
        update a_member_permission
        set delete_time = now()
        where id = #{id}
    </update>

    <update id="softDeleteMemberPermissionByMemberAndPermissionId">
        update a_member_permission
        set delete_time = now()
        where member_id = #{memberId} and permission_id = #{permissionId} and delete_time is null
    </update>

</mapper> 