<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.func.FuncMenuMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.func.FuncMenu" id="FuncMenuResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="icon"    column="icon"    />
        <result property="component"    column="component"    />
        <result property="path"    column="path"    />
        <result property="redirect"    column="redirect"    />
        <result property="hidden"    column="hidden"    />
        <result property="affix"    column="affix"    />
        <result property="breadcrumb"    column="breadcrumb"    />
        <result property="isCache"    column="is_cache"    />
    </resultMap>

    <sql id="selectFuncMenuVo">
        select id, pid, name, code, type, sort_order, status, icon, component, path, redirect, hidden, affix, breadcrumb, is_cache
        from a_func_menu
    </sql>

    <select id="selectMenusByMemberId" parameterType="Long" resultMap="FuncMenuResult">
        select distinct m.id, m.pid, m.name, m.code, m.type, m.sort_order, m.status, m.icon, m.component, m.path, m.redirect,
                        m.hidden, m.affix, m.breadcrumb, m.is_cache
        from a_func_menu m
        where m.delete_time is null
          and m.status = 1
          and m.hidden = false
          and exists (
              select 1 from a_func_menu_permission mp
              inner join a_func_role_permission rp on mp.permission_id = rp.permission_id
              inner join a_member_role mr on rp.role_id = mr.role_id
              where mr.member_id = #{memberId}
                and mp.menu_id = m.id
                and mr.delete_time is null
                and rp.delete_time is null
                and mp.delete_time is null
          )
        order by m.sort_order
    </select>

    <select id="selectMenusByRoleId" parameterType="Long" resultMap="FuncMenuResult">
        select distinct m.id, m.pid, m.name, m.code, m.type, m.sort_order, m.status, m.icon, m.component, m.path, m.redirect,
                        m.hidden, m.affix, m.breadcrumb, m.is_cache
        from a_func_menu m
        where m.delete_time is null
          and m.status = 1
          and exists (
              select 1 from a_func_menu_permission mp
              inner join a_func_role_permission rp on mp.permission_id = rp.permission_id
              where rp.role_id = #{roleId}
                and mp.menu_id = m.id
                and rp.delete_time is null
                and mp.delete_time is null
          )
        order by m.sort_order
    </select>

    <select id="selectMenusByPermissionId" parameterType="Long" resultMap="FuncMenuResult">
        select distinct m.id, m.pid, m.name, m.code, m.type, m.sort_order, m.status, m.icon, m.component, m.path, m.redirect,
                        m.hidden, m.affix, m.breadcrumb, m.is_cache
        from a_func_menu m
        inner join a_func_menu_permission mp on m.id = mp.menu_id
        where mp.permission_id = #{permissionId}
          and m.status = 1
          and m.delete_time is null
          and mp.delete_time is null
        order by m.sort_order
    </select>

    <select id="selectVisibleMenus" resultMap="FuncMenuResult">
        <include refid="selectFuncMenuVo"/>
        where status = 1
          and hidden = false
          and delete_time is null
        order by sort_order
    </select>

    <select id="selectMenuById" parameterType="Long" resultMap="FuncMenuResult">
        <include refid="selectFuncMenuVo"/>
        where id = #{id} and delete_time is null
    </select>

    <select id="selectMenuByCode" parameterType="String" resultMap="FuncMenuResult">
        <include refid="selectFuncMenuVo"/>
        where code = #{code}
          and status = 1
          and delete_time is null
    </select>
</mapper> 