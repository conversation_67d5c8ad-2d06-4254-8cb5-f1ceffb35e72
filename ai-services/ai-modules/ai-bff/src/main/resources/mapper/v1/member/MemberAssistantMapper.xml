<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.member.MemberAssistantMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.member.MemberAssistant" id="MemberAssistantResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="assistantId" column="assistant_id"/>
        <result property="templateVersion" column="template_version"/>
        <result property="modelId" column="model_id"/>
        <result property="customName" column="custom_name"/>
        <result property="settingsOverride" column="settings_override"/>
        <result property="isFavorite" column="is_favorite"/>
        <result property="isActive" column="is_active"/>
        <result property="usageCount" column="usage_count"/>
        <result property="lastUsedTime" column="last_used_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteTime" column="delete_time"/>
    </resultMap>

    <sql id="selectMemberAssistantVo">
        select id, member_id, assistant_id, template_version, model_id, custom_name, settings_override,
               is_favorite, is_active, usage_count, last_used_time, create_time, update_time, delete_time
        from a_member_assistant
    </sql>

    <select id="selectMemberAssistantById" parameterType="Long" resultMap="MemberAssistantResult">
        <include refid="selectMemberAssistantVo"/>
        where id = #{id} and delete_time is null
    </select>

    <select id="selectMemberAssistantsByMemberId" parameterType="Long" resultMap="MemberAssistantResult">
        <include refid="selectMemberAssistantVo"/>
        where member_id = #{memberId} and delete_time is null
        order by is_favorite desc, update_time desc
    </select>

    <select id="selectActiveMemberAssistantsByMemberId" parameterType="Long" resultMap="MemberAssistantResult">
        <include refid="selectMemberAssistantVo"/>
        where member_id = #{memberId} and is_active = true and delete_time is null
        order by is_favorite desc, update_time desc
    </select>

    <select id="selectFavoriteMemberAssistantsByMemberId" parameterType="Long" resultMap="MemberAssistantResult">
        <include refid="selectMemberAssistantVo"/>
        where member_id = #{memberId} and is_favorite = true and delete_time is null
        order by update_time desc
    </select>

    <insert id="insertMemberAssistant" parameterType="ai.showlab.bff.entity.domain.v1.member.MemberAssistant" useGeneratedKeys="true" keyProperty="id">
        insert into a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name,
                                      settings_override, is_favorite, is_active, create_time, update_time)
        values (#{memberId}, #{assistantId}, #{templateVersion}, #{modelId}, #{customName},
                #{settingsOverride}, #{isFavorite}, #{isActive}, now(), now())
    </insert>

    <update id="updateMemberAssistant" parameterType="ai.showlab.bff.entity.domain.v1.member.MemberAssistant">
        update a_member_assistant
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="customName != null and customName != ''">custom_name = #{customName},</if>
            <if test="settingsOverride != null and settingsOverride != ''">settings_override = #{settingsOverride},</if>
            <if test="isFavorite != null">is_favorite = #{isFavorite},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            update_time = now(),
        </trim>
        where id = #{id} and member_id = #{memberId} and delete_time is null
    </update>

    <update id="softDeleteMemberAssistant">
        update a_member_assistant
        set delete_time = now()
        where id = #{id} and member_id = #{memberId} and delete_time is null
    </update>

    <!-- ==================== 新增方法（面向外部API）==================== -->

    <!-- MemberAssistantVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.MemberAssistantVo" id="MemberAssistantVoResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="assistantId" column="assistant_id"/>
        <result property="assistantCode" column="assistant_code"/>
        <result property="assistantName" column="assistant_name"/>
        <result property="assistantDescription" column="assistant_description"/>
        <result property="assistantIconUrl" column="assistant_icon_url"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="interactionMode" column="interaction_mode"/>
        <result property="templateVersion" column="template_version"/>
        <result property="modelId" column="model_id"/>
        <result property="modelName" column="model_name"/>
        <result property="modelCode" column="model_code"/>
        <result property="customName" column="custom_name"/>
        <result property="settingsOverride" column="settings_override"/>
        <result property="isFavorite" column="is_favorite"/>
        <result property="isActive" column="is_active"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastUsedTime" column="last_used_time"/>
        <result property="usageCount" column="usage_count"/>
    </resultMap>

    <!-- MemberAssistantDetailVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.MemberAssistantDetailVo" id="MemberAssistantDetailVoResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="assistantId" column="assistant_id"/>
        <result property="assistantCode" column="assistant_code"/>
        <result property="assistantName" column="assistant_name"/>
        <result property="assistantDescription" column="assistant_description"/>
        <result property="assistantIconUrl" column="assistant_icon_url"/>
        <result property="templateVersion" column="template_version"/>
        <result property="customName" column="custom_name"/>
        <result property="settingsOverride" column="settings_override"/>
        <result property="isFavorite" column="is_favorite"/>
        <result property="isActive" column="is_active"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastUsedTime" column="last_used_time"/>
        <result property="usageCount" column="usage_count"/>
    </resultMap>

    <!-- 分页查询会员助手实例列表（返回VO对象） -->
    <select id="selectMemberAssistantListVo" resultMap="MemberAssistantVoResult">
        SELECT
            ma.id,
            ma.member_id,
            ma.assistant_id,
            a.code as assistant_code,
            a.name as assistant_name,
            a.description as assistant_description,
            a.icon_url as assistant_icon_url,
            a.category_id,
            ac.name as category_name,
            a.interaction_mode,
            ma.template_version,
            ma.model_id,
            m.name as model_name,
            m.code as model_code,
            ma.custom_name,
            ma.settings_override,
            ma.is_favorite,
            ma.is_active,
            ma.create_time,
            ma.update_time,
            ma.last_used_time,
            COALESCE(ma.usage_count, 0) as usage_count
        FROM a_member_assistant ma
        INNER JOIN a_assistant a ON ma.assistant_id = a.id
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        LEFT JOIN a_model m ON ma.model_id = m.id
        WHERE ma.member_id = #{memberId}
        AND ma.delete_time IS NULL
        AND a.delete_time IS NULL
        <if test="param.categoryId != null">
            AND a.category_id = #{param.categoryId}
        </if>
        <if test="param.interactionMode != null">
            AND a.interaction_mode = #{param.interactionMode}
        </if>
        <if test="param.isFavorite != null">
            AND ma.is_favorite = #{param.isFavorite}
        </if>
        <if test="param.isActive != null">
            AND ma.is_active = #{param.isActive}
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            AND (a.name ILIKE CONCAT('%', #{param.keyword}, '%')
                 OR ma.custom_name ILIKE CONCAT('%', #{param.keyword}, '%'))
        </if>
        ORDER BY
        <choose>
            <when test="param.sortBy == 'create_time'">
                ma.create_time
            </when>
            <when test="param.sortBy == 'custom_name'">
                ma.custom_name
            </when>
            <otherwise>
                ma.update_time
            </otherwise>
        </choose>
        <choose>
            <when test="param.sortOrder == 'asc'">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
        LIMIT #{param.pageSize} OFFSET #{param.pageSize} * (#{param.pageNum} - 1)
    </select>

    <!-- 统计会员助手实例数量 -->
    <select id="countMemberAssistants" resultType="long">
        SELECT COUNT(*)
        FROM a_member_assistant ma
        INNER JOIN a_assistant a ON ma.assistant_id = a.id
        WHERE ma.member_id = #{memberId}
        AND ma.delete_time IS NULL
        AND a.delete_time IS NULL
        <if test="param.categoryId != null">
            AND a.category_id = #{param.categoryId}
        </if>
        <if test="param.interactionMode != null">
            AND a.interaction_mode = #{param.interactionMode}
        </if>
        <if test="param.isFavorite != null">
            AND ma.is_favorite = #{param.isFavorite}
        </if>
        <if test="param.isActive != null">
            AND ma.is_active = #{param.isActive}
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            AND (a.name ILIKE CONCAT('%', #{param.keyword}, '%')
                 OR ma.custom_name ILIKE CONCAT('%', #{param.keyword}, '%'))
        </if>
    </select>

    <!-- 根据ID查询会员助手实例详情（返回VO对象） -->
    <select id="selectMemberAssistantDetailVo" resultMap="MemberAssistantDetailVoResult">
        SELECT
            ma.id,
            ma.member_id,
            ma.assistant_id,
            a.code as assistant_code,
            a.name as assistant_name,
            a.description as assistant_description,
            a.icon_url as assistant_icon_url,
            ma.template_version,
            ma.custom_name,
            ma.settings_override,
            ma.is_favorite,
            ma.is_active,
            ma.create_time,
            ma.update_time,
            ma.last_used_time,
            COALESCE(ma.usage_count, 0) as usage_count
        FROM a_member_assistant ma
        INNER JOIN a_assistant a ON ma.assistant_id = a.id
        WHERE ma.id = #{id}
        AND ma.member_id = #{memberId}
        AND ma.delete_time IS NULL
        AND a.delete_time IS NULL
    </select>

    <!-- 查询会员的激活助手实例列表（返回VO对象） -->
    <select id="selectActiveMemberAssistantsVo" parameterType="Long" resultMap="MemberAssistantVoResult">
        SELECT
            ma.id,
            ma.member_id,
            ma.assistant_id,
            a.code as assistant_code,
            a.name as assistant_name,
            a.description as assistant_description,
            a.icon_url as assistant_icon_url,
            a.category_id,
            ac.name as category_name,
            a.interaction_mode,
            ma.template_version,
            ma.model_id,
            m.name as model_name,
            m.code as model_code,
            ma.custom_name,
            ma.settings_override,
            ma.is_favorite,
            ma.is_active,
            ma.create_time,
            ma.update_time,
            ma.last_used_time,
            COALESCE(ma.usage_count, 0) as usage_count
        FROM a_member_assistant ma
        INNER JOIN a_assistant a ON ma.assistant_id = a.id
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        LEFT JOIN a_model m ON ma.model_id = m.id
        WHERE ma.member_id = #{memberId}
        AND ma.is_active = true
        AND ma.delete_time IS NULL
        AND a.delete_time IS NULL
        ORDER BY ma.is_favorite DESC, ma.update_time DESC
    </select>

    <!-- 查询会员的收藏助手实例列表（返回VO对象） -->
    <select id="selectFavoriteMemberAssistantsVo" parameterType="Long" resultMap="MemberAssistantVoResult">
        SELECT
            ma.id,
            ma.member_id,
            ma.assistant_id,
            a.code as assistant_code,
            a.name as assistant_name,
            a.description as assistant_description,
            a.icon_url as assistant_icon_url,
            a.category_id,
            ac.name as category_name,
            a.interaction_mode,
            ma.template_version,
            ma.model_id,
            m.name as model_name,
            m.code as model_code,
            ma.custom_name,
            ma.settings_override,
            ma.is_favorite,
            ma.is_active,
            ma.create_time,
            ma.update_time,
            ma.last_used_time,
            COALESCE(ma.usage_count, 0) as usage_count
        FROM a_member_assistant ma
        INNER JOIN a_assistant a ON ma.assistant_id = a.id
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        LEFT JOIN a_model m ON ma.model_id = m.id
        WHERE ma.member_id = #{memberId}
        AND ma.is_favorite = true
        AND ma.delete_time IS NULL
        AND a.delete_time IS NULL
        ORDER BY ma.update_time DESC
    </select>

    <!-- 检查会员是否已创建指定助手的实例 -->
    <select id="existsMemberAssistantInstance" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM a_member_assistant ma
        WHERE ma.member_id = #{memberId}
        AND ma.assistant_id = #{assistantId}
        AND ma.delete_time IS NULL
    </select>

    <!-- 根据会员ID和助手ID查询会员助手实例 -->
    <select id="selectMemberAssistantByMemberAndAssistant" resultMap="MemberAssistantResult">
        <include refid="selectMemberAssistantVo"/>
        WHERE member_id = #{memberId}
        AND assistant_id = #{assistantId}
        AND delete_time IS NULL
    </select>

    <!-- 更新会员助手实例状态 -->
    <update id="updateMemberAssistantStatus">
        UPDATE a_member_assistant
        SET is_active = #{isActive},
            update_time = now()
        WHERE id = #{id}
        AND member_id = #{memberId}
        AND delete_time IS NULL
    </update>

    <!-- 更新会员助手实例收藏状态 -->
    <update id="updateMemberAssistantFavorite">
        UPDATE a_member_assistant
        SET is_favorite = #{isFavorite},
            update_time = now()
        WHERE id = #{id}
        AND member_id = #{memberId}
        AND delete_time IS NULL
    </update>

    <!-- 更新会员助手实例名称 -->
    <update id="updateMemberAssistantName">
        UPDATE a_member_assistant
        SET custom_name = #{customName},
            update_time = now()
        WHERE id = #{id}
        AND member_id = #{memberId}
        AND delete_time IS NULL
    </update>

    <!-- 更新会员助手实例模型 -->
    <update id="updateMemberAssistantModel">
        UPDATE a_member_assistant
        SET model_id = #{modelId},
            update_time = now()
        WHERE id = #{id}
        AND member_id = #{memberId}
        AND delete_time IS NULL
    </update>

    <!-- 更新会员助手实例参数配置 -->
    <update id="updateMemberAssistantSettings">
        UPDATE a_member_assistant
        SET settings_override = #{settingsOverride},
            update_time = now()
        WHERE id = #{id}
        AND member_id = #{memberId}
        AND delete_time IS NULL
    </update>

</mapper>