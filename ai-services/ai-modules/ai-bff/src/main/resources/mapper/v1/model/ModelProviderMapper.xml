<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelProviderMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.model.ModelProvider" id="ModelProviderResult">
        <result property="id" column="id"/>
        <result property="providerName" column="provider_name"/>
        <result property="providerKey" column="provider_key"/>
        <result property="channelType" column="channel_type"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="baseUrl" column="base_url"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectModelProviderVo">
        select id, provider_name, provider_key, channel_type, sort_order, base_url, description, create_time, update_time from a_model_provider
    </sql>

    <select id="selectModelProviderById" parameterType="Long" resultMap="ModelProviderResult">
        <include refid="selectModelProviderVo"/>
        where id = #{id}
    </select>

    <select id="selectAllModelProviders" resultMap="ModelProviderResult">
        <include refid="selectModelProviderVo"/>
        order by sort_order asc
    </select>

</mapper> 