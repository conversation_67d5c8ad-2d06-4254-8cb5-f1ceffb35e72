<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingOrderMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingOrder" id="BillingOrderResult">
        <result property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="memberId" column="member_id"/>
        <result property="packageId" column="package_id"/>
        <result property="amount" column="amount"/>
        <result property="currencyId" column="currency_id"/>
        <result property="status" column="status"/>
        <result property="paymentGatewayId" column="payment_gateway_id"/>
        <result property="paidTime" column="paid_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectBillingOrderVo">
        select id, order_no, member_id, package_id, amount, currency_id, status,
               payment_gateway_id, paid_time, create_time
        from a_billing_order
    </sql>

    <select id="selectOrdersByMemberId" parameterType="Long" resultMap="BillingOrderResult">
        <include refid="selectBillingOrderVo"/>
        where member_id = #{memberId} and delete_time is null
        order by create_time desc
    </select>

    <select id="selectOrderByOrderNo" resultMap="BillingOrderResult">
        <include refid="selectBillingOrderVo"/>
        where order_no = #{orderNo}
          and member_id = #{memberId}
          and delete_time is null
    </select>

    <insert id="insertOrder" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingOrder" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_order (order_no, member_id, package_id, package_info_snapshot, amount, currency_id, status, create_time, update_time)
        values (#{orderNo}, #{memberId}, #{packageId}, #{packageInfoSnapshot}, #{amount}, #{currencyId}, #{status}, now(), now())
    </insert>

    <update id="updateOrderStatus">
        update a_billing_order
        set status = #{status},
            payment_gateway_id = #{paymentGatewayId},
            gateway_transaction_id = #{gatewayTransactionId},
            paid_time = now(),
            update_time = now()
        where order_no = #{orderNo}
          and member_id = #{memberId}
          and status = 1 -- 只能更新待支付的订单
          and delete_time is null
    </update>

</mapper> 