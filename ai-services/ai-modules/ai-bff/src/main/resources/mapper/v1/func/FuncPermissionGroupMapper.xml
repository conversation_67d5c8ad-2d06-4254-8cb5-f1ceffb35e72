<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.func.FuncPermissionGroupMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.func.FuncPermissionGroup" id="FuncPermissionGroupResult">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <sql id="selectFuncPermissionGroupVo">
        select id, pid, code, name, description, sort_order
        from a_func_permission_group
    </sql>

    <select id="selectAllPermissionGroups" resultMap="FuncPermissionGroupResult">
        <include refid="selectFuncPermissionGroupVo"/>
        where delete_time is null
        order by sort_order asc
    </select>

    <select id="selectPermissionGroupById" parameterType="Long" resultMap="FuncPermissionGroupResult">
        <include refid="selectFuncPermissionGroupVo"/>
        where id = #{id} and delete_time is null
    </select>

    <select id="selectPermissionGroupsByPid" parameterType="Long" resultMap="FuncPermissionGroupResult">
        <include refid="selectFuncPermissionGroupVo"/>
        where pid = #{pid} and delete_time is null
        order by sort_order asc
    </select>

</mapper> 