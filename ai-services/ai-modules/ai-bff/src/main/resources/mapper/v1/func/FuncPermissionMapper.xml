<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.func.FuncPermissionMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.func.FuncPermission" id="FuncPermissionResult">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="actionType" column="action_type"/>
        <result property="resourceType" column="resource_type"/>
    </resultMap>

    <sql id="selectFuncPermissionVo">
        select id, group_id, code, name, description, action_type, resource_type
        from a_func_permission
    </sql>

    <select id="selectPermissionsByMemberId" parameterType="Long" resultMap="FuncPermissionResult">
        select distinct p.id, p.group_id, p.code, p.name, p.description, p.action_type, p.resource_type
        from a_func_permission p
        inner join a_func_role_permission rp on p.id = rp.permission_id
        inner join a_member_role mr on rp.role_id = mr.role_id
        where mr.member_id = #{memberId}
          and p.delete_time is null
          and rp.delete_time is null
          and mr.delete_time is null
    </select>

    <select id="selectPermissionsByRoleId" parameterType="Long" resultMap="FuncPermissionResult">
        <include refid="selectFuncPermissionVo"/>
        p
        inner join a_func_role_permission rp on p.id = rp.permission_id
        where rp.role_id = #{roleId}
          and p.delete_time is null
          and rp.delete_time is null
    </select>

    <select id="checkMemberHasPermission" resultType="java.lang.Integer">
        select count(1)
        from a_func_permission p
        inner join a_func_role_permission rp on p.id = rp.permission_id
        inner join a_member_role mr on rp.role_id = mr.role_id
        where mr.member_id = #{memberId}
          and p.code = #{permissionCode}
          and p.delete_time is null
          and rp.delete_time is null
          and mr.delete_time is null
    </select>

</mapper> 