<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.assistant.AssistantParamMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.assistant.AssistantParam" id="AssistantParamResult">
        <result property="id" column="id"/>
        <result property="assistantId" column="assistant_id"/>
        <result property="key" column="key"/>
        <result property="label" column="label"/>
        <result property="paramType" column="param_type"/>
        <result property="defaultValue" column="default_value"/>
        <result property="options" column="options"/>
        <result property="description" column="description"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <sql id="selectAssistantParamVo">
        select id, assistant_id, "key", label, param_type, default_value, options, description, sort_order
        from a_assistant_param
    </sql>

    <select id="selectParamsByAssistantId" parameterType="Long" resultMap="AssistantParamResult">
        <include refid="selectAssistantParamVo"/>
        where assistant_id = #{assistantId} and delete_time is null
        order by sort_order asc
    </select>

</mapper> 