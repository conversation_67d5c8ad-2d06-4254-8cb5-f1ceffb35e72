<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingPriceMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingPrice" id="BillingPriceResult">
        <result property="id" column="id"/>
        <result property="modelId" column="model_id"/>
        <result property="planId" column="plan_id"/>
        <result property="memberLevel" column="member_level"/>
        <result property="price" column="price"/>
        <result property="currencyId" column="currency_id"/>
    </resultMap>

    <sql id="selectBillingPriceVo">
        select id, model_id, plan_id, member_level, price, currency_id
        from a_billing_price
    </sql>

    <select id="selectPricesByPlanId" parameterType="Long" resultMap="BillingPriceResult">
        <include refid="selectBillingPriceVo"/>
        where plan_id = #{planId} and delete_time is null
    </select>

    <select id="selectPriceByModelAndLevel" resultMap="BillingPriceResult">
        <include refid="selectBillingPriceVo"/>
        where model_id = #{modelId}
          and member_level = #{memberLevel}
          and delete_time is null
        limit 1
    </select>

</mapper> 