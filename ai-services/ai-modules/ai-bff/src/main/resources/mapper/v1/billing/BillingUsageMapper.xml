<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingUsageMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingUsage" id="BillingUsageResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="modelId" column="model_id"/>
        <result property="planId" column="plan_id"/>
        <result property="unit" column="unit"/>
        <result property="amount" column="amount"/>
        <result property="durationMs" column="duration_ms"/>
        <result property="resultSize" column="result_size"/>
        <result property="usedTime" column="used_time"/>
    </resultMap>

    <sql id="selectBillingUsageVo">
        select id, member_id, model_id, plan_id, unit, amount, duration_ms, result_size, used_time
        from a_billing_usage
    </sql>

    <select id="selectUsagesByMemberId" parameterType="Long" resultMap="BillingUsageResult">
        <include refid="selectBillingUsageVo"/>
        where member_id = #{memberId} and delete_time is null
        order by used_time desc
    </select>

    <insert id="insertUsage" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingUsage" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_usage (member_id, model_id, plan_id, unit, amount, duration_ms, result_size, used_time, create_time, update_time)
        values (#{memberId}, #{modelId}, #{planId}, #{unit}, #{amount}, #{durationMs}, #{resultSize}, #{usedTime}, now(), now())
    </insert>

</mapper> 