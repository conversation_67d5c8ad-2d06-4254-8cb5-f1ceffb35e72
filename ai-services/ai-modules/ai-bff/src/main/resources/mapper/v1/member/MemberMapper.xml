<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.member.MemberMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.member.Member" id="MemberResult">
        <result property="id" column="id"/>
        <result property="memberType" column="member_type"/>
        <result property="memberLevel" column="member_level"/>
        <result property="username" column="username"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="gender" column="gender"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="invitationCode" column="invitation_code"/>
        <result property="inviterId" column="inviter_id"/>
        <result property="status" column="status"/>
        <result property="countryId" column="country_id"/>
        <result property="langId" column="lang_id"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="lastLoginTime" column="last_login_time"/>
    </resultMap>

    <!-- 面向前端，安全的会员信息 -->
    <sql id="selectMemberProfileVo">
        select id, username, nickname, avatar, gender, email, phone, status,
        country_id, lang_id, last_login_time
        from a_member
    </sql>

    <!-- 面向服务内部，包含部分敏感信息 -->
    <sql id="selectMemberInternalVo">
        select id, member_type, member_level, username, nickname, avatar, gender, email, phone, invitation_code,
        inviter_id, status, country_id, lang_id, last_login_ip, last_login_time
        from a_member
    </sql>

    <select id="selectMemberById" parameterType="Long" resultMap="MemberResult">
        <include refid="selectMemberProfileVo"/>
        where id = #{id} and delete_time is null
    </select>

    <select id="selectMemberByUsername" parameterType="String" resultMap="MemberResult">
        <include refid="selectMemberInternalVo"/>
        where username = #{username} and delete_time is null
    </select>

    <select id="selectMemberByEmail" parameterType="String" resultMap="MemberResult">
        <include refid="selectMemberInternalVo"/>
        where email = #{email} and delete_time is null
    </select>

    <select id="selectMemberByPhone" parameterType="String" resultMap="MemberResult">
        <include refid="selectMemberInternalVo"/>
        where phone = #{phone} and delete_time is null
    </select>

    <select id="selectMemberByInvitationCode" parameterType="String" resultMap="MemberResult">
        <include refid="selectMemberProfileVo"/>
        where invitation_code = #{invitationCode} and delete_time is null and status = 1
    </select>

    <insert id="insertMember" parameterType="ai.showlab.bff.entity.domain.v1.member.Member" useGeneratedKeys="true" keyProperty="id">
        insert into a_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">username,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="gender != null">gender,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="invitationCode != null and invitationCode != ''">invitation_code,</if>
            <if test="inviterId != null">inviter_id,</if>
            <if test="status != null">status,</if>
            <if test="countryId != null">country_id,</if>
            <if test="langId != null">lang_id,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">#{username},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="gender != null">#{gender},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="invitationCode != null and invitationCode != ''">#{invitationCode},</if>
            <if test="inviterId != null">#{inviterId},</if>
            <if test="status != null">#{status},</if>
            <if test="countryId != null">#{countryId},</if>
            <if test="langId != null">#{langId},</if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateMemberProfile" parameterType="ai.showlab.bff.entity.domain.v1.member.Member">
        update a_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="countryId != null">country_id = #{countryId},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            update_time = now(),
        </trim>
        where id = #{id} and delete_time is null
    </update>

    <update id="updateMemberLastLogin" parameterType="ai.showlab.bff.entity.domain.v1.member.Member">
        update a_member
        set last_login_ip = #{lastLoginIp},
            last_login_time = #{lastLoginTime},
            update_time = now()
        where id = #{id} and delete_time is null
    </update>
</mapper> 