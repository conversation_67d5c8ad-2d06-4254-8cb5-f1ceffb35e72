<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.assistant.AssistantMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.assistant.Assistant" id="AssistantResult">
        <result property="id" column="id"/>
        <result property="categoryId" column="category_id"/>
        <result property="ownerMemberId" column="owner_member_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="billingPackageIds" column="billing_package_ids"/>
        <result property="knowledgeIds" column="knowledge_ids"/>
        <result property="description" column="description"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="promptTemplate" column="prompt_template"/>
        <result property="interactionMode" column="interaction_mode"/>
        <result property="modelSuggestions" column="model_suggestions"/>
        <result property="version" column="version"/>
        <result property="status" column="status"/>
        <result property="usageCount" column="usage_count"/>
        <result property="isPublic" column="is_public"/>
        <result property="isPreset" column="is_preset"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <sql id="selectAssistantVo">
        select id, category_id, owner_member_id, code, name, billing_package_ids, knowledge_ids, description,
        icon_url, prompt_template, interaction_mode, model_suggestions, version, status,
        usage_count, is_public, is_preset, sort_order
        from a_assistant
    </sql>

    <select id="selectPublicAssistants" resultMap="AssistantResult">
        <include refid="selectAssistantVo"/>
        where is_public = true
        and status = 2 -- 2: 已发布
        and delete_time is null
        order by sort_order desc, usage_count desc
    </select>

    <select id="selectAssistantsByMemberId" parameterType="Long" resultMap="AssistantResult">
        <include refid="selectAssistantVo"/>
        where owner_member_id = #{memberId} and delete_time is null
        order by create_time desc
    </select>

    <select id="selectAssistantById" parameterType="Long" resultMap="AssistantResult">
        <include refid="selectAssistantVo"/>
        where id = #{id} and delete_time is null
    </select>

    <update id="incrementUsageCount" parameterType="Long">
        update a_assistant
        set usage_count = usage_count + 1
        where id = #{id}
    </update>

    <!-- ==================== 新增方法（面向外部API）==================== -->

    <!-- AssistantListVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.AssistantListVo" id="AssistantListVoResult">
        <result property="id" column="id"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="interactionMode" column="interaction_mode"/>
        <result property="status" column="status"/>
        <result property="usageCount" column="usage_count"/>
        <result property="isPublic" column="is_public"/>
        <result property="isPreset" column="is_preset"/>
        <result property="version" column="version"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isAdded" column="is_added"/>
        <result property="isFavorited" column="is_favorited"/>
    </resultMap>

    <!-- AssistantDetailVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.AssistantDetailVo" id="AssistantDetailVoResult">
        <result property="id" column="id"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="ownerMemberId" column="owner_member_id"/>
        <result property="ownerNickname" column="owner_nickname"/>
        <result property="revenueShareRate" column="revenue_share_rate"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="promptTemplate" column="prompt_template"/>
        <result property="interactionMode" column="interaction_mode"/>
        <result property="modelSuggestions" column="model_suggestions"/>
        <result property="version" column="version"/>
        <result property="status" column="status"/>
        <result property="usageCount" column="usage_count"/>
        <result property="isPublic" column="is_public"/>
        <result property="isPreset" column="is_preset"/>
        <result property="billingPackageIds" column="billing_package_ids"/>
        <result property="knowledgeIds" column="knowledge_ids"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isAdded" column="is_added"/>
        <result property="isFavorited" column="is_favorited"/>
        <result property="memberAssistantId" column="member_assistant_id"/>
    </resultMap>

    <!-- 分页查询助手列表（返回VO对象） -->
    <select id="selectAssistantListVoByCondition" parameterType="ai.showlab.bff.entity.param.AssistantListParam" resultMap="AssistantListVoResult">
        SELECT
            a.id,
            a.category_id,
            ac.name as category_name,
            a.code,
            a.name,
            a.description,
            a.icon_url,
            a.interaction_mode,
            a.status,
            a.usage_count,
            a.is_public,
            a.is_preset,
            a.version,
            a.create_time,
            a.update_time,
            false as is_added,
            false as is_favorited
        FROM a_assistant a
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        WHERE a.delete_time IS NULL
        <if test="categoryId != null">
            AND a.category_id = #{categoryId}
        </if>
        <if test="interactionMode != null">
            AND a.interaction_mode = #{interactionMode}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        <if test="isPublic != null">
            AND a.is_public = #{isPublic}
        </if>
        <if test="isPreset != null">
            AND a.is_preset = #{isPreset}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (a.name ILIKE CONCAT('%', #{keyword}, '%') OR a.description ILIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY
        <choose>
            <when test="sortBy == 'create_time'">
                a.create_time
            </when>
            <when test="sortBy == 'update_time'">
                a.update_time
            </when>
            <otherwise>
                a.usage_count
            </otherwise>
        </choose>
        <choose>
            <when test="sortOrder == 'asc'">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
        LIMIT #{pageSize} OFFSET #{pageSize} * (#{pageNum} - 1)
    </select>

    <!-- 统计符合条件的助手数量 -->
    <select id="countAssistantsByCondition" parameterType="ai.showlab.bff.entity.param.AssistantListParam" resultType="long">
        SELECT COUNT(*)
        FROM a_assistant a
        WHERE a.delete_time IS NULL
        <if test="categoryId != null">
            AND a.category_id = #{categoryId}
        </if>
        <if test="interactionMode != null">
            AND a.interaction_mode = #{interactionMode}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        <if test="isPublic != null">
            AND a.is_public = #{isPublic}
        </if>
        <if test="isPreset != null">
            AND a.is_preset = #{isPreset}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (a.name ILIKE CONCAT('%', #{keyword}, '%') OR a.description ILIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>

    <!-- 根据ID查询助手详情（返回VO对象） -->
    <select id="selectAssistantDetailVoById" parameterType="Long" resultMap="AssistantDetailVoResult">
        SELECT
            a.id,
            a.category_id,
            ac.name as category_name,
            a.owner_member_id,
            m.nickname as owner_nickname,
            a.revenue_share_rate,
            a.code,
            a.name,
            a.description,
            a.icon_url,
            a.prompt_template,
            a.interaction_mode,
            a.model_suggestions,
            a.version,
            a.status,
            a.usage_count,
            a.is_public,
            a.is_preset,
            a.billing_package_ids,
            a.knowledge_ids,
            a.create_time,
            a.update_time,
            false as is_added,
            false as is_favorited,
            null as member_assistant_id
        FROM a_assistant a
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        LEFT JOIN a_member m ON a.owner_member_id = m.id
        WHERE a.id = #{id} AND a.delete_time IS NULL
    </select>

    <!-- 根据编码查询助手详情（返回VO对象） -->
    <select id="selectAssistantDetailVoByCode" parameterType="String" resultMap="AssistantDetailVoResult">
        SELECT
            a.id,
            a.category_id,
            ac.name as category_name,
            a.owner_member_id,
            m.nickname as owner_nickname,
            a.revenue_share_rate,
            a.code,
            a.name,
            a.description,
            a.icon_url,
            a.prompt_template,
            a.interaction_mode,
            a.model_suggestions,
            a.version,
            a.status,
            a.usage_count,
            a.is_public,
            a.is_preset,
            a.billing_package_ids,
            a.knowledge_ids,
            a.create_time,
            a.update_time,
            false as is_added,
            false as is_favorited,
            null as member_assistant_id
        FROM a_assistant a
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        LEFT JOIN a_member m ON a.owner_member_id = m.id
        WHERE a.code = #{code} AND a.delete_time IS NULL
    </select>

    <!-- 查询热门助手列表（返回VO对象） -->
    <select id="selectPopularAssistantsVo" parameterType="int" resultMap="AssistantListVoResult">
        SELECT
            a.id,
            a.category_id,
            ac.name as category_name,
            a.code,
            a.name,
            a.description,
            a.icon_url,
            a.interaction_mode,
            a.status,
            a.usage_count,
            a.is_public,
            a.is_preset,
            a.version,
            a.create_time,
            a.update_time,
            false as is_added,
            false as is_favorited
        FROM a_assistant a
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        WHERE a.delete_time IS NULL
        AND a.is_public = true
        AND a.status = 3 -- 已发布
        ORDER BY a.usage_count DESC, a.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计指定分类下的助手数量 -->
    <select id="countAssistantsByCategory" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM a_assistant a
        WHERE a.category_id = #{categoryId}
        AND a.delete_time IS NULL
        AND a.status = 3 -- 已发布
    </select>

    <!-- 根据编码查询助手信息 -->
    <select id="selectAssistantByCode" parameterType="String" resultMap="AssistantResult">
        <include refid="selectAssistantVo"/>
        WHERE code = #{code} AND delete_time IS NULL
    </select>

    <!-- 查询会员可访问的助手列表（返回VO对象） -->
    <select id="selectAccessibleAssistantsVo" resultMap="AssistantListVoResult">
        SELECT
            a.id,
            a.category_id,
            ac.name as category_name,
            a.code,
            a.name,
            a.description,
            a.icon_url,
            a.interaction_mode,
            a.status,
            a.usage_count,
            a.is_public,
            a.is_preset,
            a.version,
            a.create_time,
            a.update_time,
            CASE WHEN ma.id IS NOT NULL THEN true ELSE false END as is_added,
            CASE WHEN af.id IS NOT NULL THEN true ELSE false END as is_favorited
        FROM a_assistant a
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        LEFT JOIN a_member_assistant ma ON a.id = ma.assistant_id AND ma.member_id = #{memberId} AND ma.delete_time IS NULL
        LEFT JOIN a_assistant_favorite af ON a.id = af.assistant_id AND af.member_id = #{memberId} AND af.delete_time IS NULL
        WHERE a.delete_time IS NULL
        AND (a.is_public = true OR a.owner_member_id = #{memberId})
        AND a.status = 3 -- 已发布
        <if test="param.categoryId != null">
            AND a.category_id = #{param.categoryId}
        </if>
        <if test="param.interactionMode != null">
            AND a.interaction_mode = #{param.interactionMode}
        </if>
        <if test="param.isPublic != null">
            AND a.is_public = #{param.isPublic}
        </if>
        <if test="param.isPreset != null">
            AND a.is_preset = #{param.isPreset}
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            AND (a.name ILIKE CONCAT('%', #{param.keyword}, '%') OR a.description ILIKE CONCAT('%', #{param.keyword}, '%'))
        </if>
        ORDER BY
        <choose>
            <when test="param.sortBy == 'create_time'">
                a.create_time
            </when>
            <when test="param.sortBy == 'update_time'">
                a.update_time
            </when>
            <otherwise>
                a.usage_count
            </otherwise>
        </choose>
        <choose>
            <when test="param.sortOrder == 'asc'">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
        LIMIT #{param.pageSize} OFFSET #{param.pageSize} * (#{param.pageNum} - 1)
    </select>

    <!-- 统计会员可访问的助手数量 -->
    <select id="countAccessibleAssistants" resultType="long">
        SELECT COUNT(*)
        FROM a_assistant a
        WHERE a.delete_time IS NULL
        AND (a.is_public = true OR a.owner_member_id = #{memberId})
        AND a.status = 3 -- 已发布
        <if test="param.categoryId != null">
            AND a.category_id = #{param.categoryId}
        </if>
        <if test="param.interactionMode != null">
            AND a.interaction_mode = #{param.interactionMode}
        </if>
        <if test="param.isPublic != null">
            AND a.is_public = #{param.isPublic}
        </if>
        <if test="param.isPreset != null">
            AND a.is_preset = #{param.isPreset}
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            AND (a.name ILIKE CONCAT('%', #{param.keyword}, '%') OR a.description ILIKE CONCAT('%', #{param.keyword}, '%'))
        </if>
    </select>

    <!-- 查询会员收藏的助手列表（返回VO对象） -->
    <select id="selectFavoriteAssistantsVo" parameterType="Long" resultMap="AssistantListVoResult">
        SELECT
            a.id,
            a.category_id,
            ac.name as category_name,
            a.code,
            a.name,
            a.description,
            a.icon_url,
            a.interaction_mode,
            a.status,
            a.usage_count,
            a.is_public,
            a.is_preset,
            a.version,
            a.create_time,
            a.update_time,
            CASE WHEN ma.id IS NOT NULL THEN true ELSE false END as is_added,
            true as is_favorited
        FROM a_assistant a
        INNER JOIN a_assistant_favorite af ON a.id = af.assistant_id
        LEFT JOIN a_assistant_category ac ON a.category_id = ac.id
        LEFT JOIN a_member_assistant ma ON a.id = ma.assistant_id AND ma.member_id = #{memberId} AND ma.delete_time IS NULL
        WHERE af.member_id = #{memberId}
        AND af.delete_time IS NULL
        AND a.delete_time IS NULL
        ORDER BY af.create_time DESC
    </select>

    <!-- 查询所有助手（内部方法） -->
    <select id="selectAllAssistants" resultMap="AssistantResult">
        <include refid="selectAssistantVo"/>
        WHERE delete_time IS NULL
        ORDER BY sort_order DESC, create_time DESC
    </select>

</mapper>