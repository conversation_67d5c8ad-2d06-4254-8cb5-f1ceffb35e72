<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingPackageMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingPackage" id="BillingPackageResult">
        <result property="id" column="id"/>
        <result property="ownerMemberId" column="owner_member_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="type" column="type"/>
        <result property="price" column="price"/>
        <result property="currencyId" column="currency_id"/>
        <result property="creditsGranted" column="credits_granted"/>
        <result property="validityDays" column="validity_days"/>
        <result property="renewalIntervalUnit" column="renewal_interval_unit"/>
        <result property="memberLevelGrant" column="member_level_grant"/>
        <result property="status" column="status"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <sql id="selectBillingPackageVo">
        select id, owner_member_id, code, name, description, type, price, currency_id, credits_granted,
               validity_days, renewal_interval_unit, member_level_grant, status, sort_order
        from a_billing_package
    </sql>

    <select id="selectAvailablePackages" resultMap="BillingPackageResult">
        <include refid="selectBillingPackageVo"/>
        where owner_member_id is null
          and status = 2 -- 2: 上架
          and delete_time is null
        order by sort_order asc
    </select>

    <select id="selectPackageById" parameterType="Long" resultMap="BillingPackageResult">
        <include refid="selectBillingPackageVo"/>
        where id = #{id}
          and status = 2 -- 2: 上架
          and delete_time is null
    </select>

</mapper> 