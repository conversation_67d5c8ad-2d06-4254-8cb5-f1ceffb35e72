<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.member.MemberAuthMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.member.MemberAuth" id="MemberAuthResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="authType" column="auth_type"/>
        <result property="identifier" column="identifier"/>
        <result property="isVerified" column="is_verified"/>
    </resultMap>

    <resultMap type="ai.showlab.bff.entity.domain.v1.member.MemberAuth" id="MemberAuthWithCredentialResult" extends="MemberAuthResult">
        <result property="credential" column="credential"/>
    </resultMap>

    <sql id="selectMemberAuthVo">
        select id, member_id, auth_type, identifier, is_verified
        from a_member_auth
    </sql>

    <!-- 仅用于登录验证，严禁用于其他场景 -->
    <select id="selectAuthForVerification" resultMap="MemberAuthWithCredentialResult">
        select id, member_id, auth_type, identifier, credential, is_verified
        from a_member_auth
        where auth_type = #{authType} and identifier = #{identifier} and delete_time is null
    </select>

    <select id="selectMemberAuthByMemberId" parameterType="Long" resultMap="MemberAuthResult">
        <include refid="selectMemberAuthVo"/>
        where member_id = #{memberId} and delete_time is null
    </select>

    <select id="selectMemberAuthByIdentifier" resultMap="MemberAuthResult">
        <include refid="selectMemberAuthVo"/>
        where auth_type = #{authType} and identifier = #{identifier} and delete_time is null
    </select>
    <select id="selectMemberAuthByMemberIdAndAuthType" resultType="ai.showlab.bff.entity.domain.v1.member.MemberAuth">
        <include refid="selectMemberAuthVo"/>
        where member_id={memberId} and auth_type = #{authType} and delete_time is null
    </select>

    <insert id="insertMemberAuth" parameterType="ai.showlab.bff.entity.domain.v1.member.MemberAuth" useGeneratedKeys="true" keyProperty="id">
        insert into a_member_auth (member_id, auth_type, identifier, credential, is_verified, create_time, update_time)
        values (#{memberId}, #{authType}, #{identifier}, #{credential}, #{isVerified}, now(), now())
    </insert>

    <update id="updateMemberAuthCredential">
        update a_member_auth
        set credential = #{credential},
            update_time = now()
        where member_id = #{memberId} and auth_type = #{authType} and delete_time is null
    </update>

    <update id="updateMemberAuthVerifyStatus">
        update a_member_auth
        set is_verified = #{isVerified},
            update_time = now()
        where member_id = #{memberId} and auth_type = #{authType} and delete_time is null
    </update>

    <update id="softDeleteMemberAuth" parameterType="Long">
        update a_member_auth
        set delete_time = now()
        where id = #{id}
    </update>

</mapper> 