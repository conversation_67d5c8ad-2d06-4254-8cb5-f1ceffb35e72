<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelApiKeyMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.model.ModelApiKey" id="ModelApiKeyResult">
        <result property="id" column="id"/>
        <result property="providerId" column="provider_id"/>
        <result property="apiKey" column="api_key"/>
        <result property="apiEndpointOverride" column="api_endpoint_override"/>
        <result property="priority" column="priority"/>
        <result property="weight" column="weight"/>
        <result property="quota" column="quota"/>
        <result property="usedQuota" column="used_quota"/>
        <result property="currencyId" column="currency_id"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="lastUsedTime" column="last_used_time"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectModelApiKeyVo">
        select id, provider_id, api_key, api_endpoint_override, priority, weight, quota, used_quota,
               currency_id, status, description, last_used_time, delete_time, create_time, update_time
        from a_model_api_key
    </sql>

    <select id="selectModelApiKeyById" parameterType="Long" resultMap="ModelApiKeyResult">
        <include refid="selectModelApiKeyVo"/>
        where id = #{id} and delete_time is null
    </select>

    <select id="selectModelApiKeysByProviderId" parameterType="Long" resultMap="ModelApiKeyResult">
        <include refid="selectModelApiKeyVo"/>
        where provider_id = #{providerId} and delete_time is null
        order by priority asc, weight desc
    </select>
    
    <select id="selectModelApiKeyByApiKey" parameterType="String" resultMap="ModelApiKeyResult">
        <include refid="selectModelApiKeyVo"/>
        where api_key = #{apiKey} and delete_time is null
    </select>

    <insert id="insertModelApiKey" parameterType="ai.showlab.bff.entity.domain.v1.model.ModelApiKey" useGeneratedKeys="true" keyProperty="id">
        insert into a_model_api_key
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="providerId != null">provider_id,</if>
            <if test="apiKey != null and apiKey != ''">api_key,</if>
            <if test="apiEndpointOverride != null and apiEndpointOverride != ''">api_endpoint_override,</if>
            <if test="priority != null">priority,</if>
            <if test="weight != null">weight,</if>
            <if test="quota != null">quota,</if>
            <if test="usedQuota != null">used_quota,</if>
            <if test="currencyId != null">currency_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="description != null and description != ''">description,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="providerId != null">#{providerId},</if>
            <if test="apiKey != null and apiKey != ''">#{apiKey},</if>
            <if test="apiEndpointOverride != null and apiEndpointOverride != ''">#{apiEndpointOverride},</if>
            <if test="priority != null">#{priority},</if>
            <if test="weight != null">#{weight},</if>
            <if test="quota != null">#{quota},</if>
            <if test="usedQuota != null">#{usedQuota},</if>
            <if test="currencyId != null">#{currencyId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="description != null and description != ''">#{description},</if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateModelApiKey" parameterType="ai.showlab.bff.entity.domain.v1.model.ModelApiKey">
        update a_model_api_key
        <trim prefix="SET" suffixOverrides=",">
            <if test="priority != null">priority = #{priority},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="quota != null">quota = #{quota},</if>
            <if test="usedQuota != null">used_quota = #{usedQuota},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="lastUsedTime != null">last_used_time = #{lastUsedTime},</if>
            update_time = now(),
        </trim>
        where id = #{id} and delete_time is null
    </update>

    <update id="softDeleteModelApiKeyById" parameterType="Long">
        update a_model_api_key
        set delete_time = now()
        where id = #{id}
    </update>

</mapper> 