<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingBalanceMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingBalance" id="BillingBalanceResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="balance" column="balance"/>
        <result property="frozenAmount" column="frozen_amount"/>
        <result property="currencyId" column="currency_id"/>
    </resultMap>

    <sql id="selectBillingBalanceVo">
        select id, member_id, balance, frozen_amount, currency_id
        from a_billing_balance
    </sql>

    <select id="selectBalanceByMemberId" parameterType="Long" resultMap="BillingBalanceResult">
        <include refid="selectBillingBalanceVo"/>
        where member_id = #{memberId} and delete_time is null
    </select>

    <insert id="insertBalance" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingBalance" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_balance (member_id, balance, frozen_amount, currency_id, create_time, update_time)
        values (#{memberId}, #{balance}, #{frozenAmount}, #{currencyId}, now(), now())
    </insert>

    <update id="updateBalance">
        update a_billing_balance
        set balance = balance + #{changeAmount},
            update_time = now()
        where member_id = #{memberId}
          and delete_time is null
    </update>

    <update id="updateFrozenAmount">
        update a_billing_balance
        set frozen_amount = frozen_amount + #{changeAmount},
            update_time = now()
        where member_id = #{memberId}
          and delete_time is null
    </update>

</mapper> 