<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.knowledge.KnowledgeDocMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.knowledge.KnowledgeDoc" id="KnowledgeDocResult">
        <result property="id" column="id"/>
        <result property="knowledgeId" column="knowledge_id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="fileSize" column="file_size"/>
        <result property="processingStatus" column="processing_status"/>
        <result property="errorMessage" column="error_message"/>
        <result property="chunkCount" column="chunk_count"/>
        <result property="charCount" column="char_count"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectKnowledgeDocVo">
        select id, knowledge_id, file_name, file_type, file_size, processing_status,
               error_message, chunk_count, char_count, create_time
        from a_knowledge_doc
    </sql>

    <select id="selectDocsByKnowledgeId" parameterType="Long" resultMap="KnowledgeDocResult">
        <include refid="selectKnowledgeDocVo"/>
        where knowledge_id = #{knowledgeId} and delete_time is null
        order by create_time desc
    </select>

    <select id="selectDocById" parameterType="Long" resultMap="KnowledgeDocResult">
        <include refid="selectKnowledgeDocVo"/>
        d
        inner join a_knowledge k on d.knowledge_id = k.id
        where d.id = #{docId}
          and k.member_id = #{memberId}
          and d.delete_time is null
    </select>

    <insert id="insertDoc" parameterType="ai.showlab.bff.entity.domain.v1.knowledge.KnowledgeDoc" useGeneratedKeys="true" keyProperty="id">
        insert into a_knowledge_doc (knowledge_id, file_name, file_type, file_size, storage_path, processing_status, create_time, update_time)
        values (#{knowledgeId}, #{fileName}, #{fileType}, #{fileSize}, #{storagePath}, #{processingStatus}, now(), now())
    </insert>

    <update id="updateDocStatus" parameterType="ai.showlab.bff.entity.domain.v1.knowledge.KnowledgeDoc">
        update a_knowledge_doc
        set processing_status = #{processingStatus},
            error_message = #{errorMessage},
            chunk_count = #{chunkCount},
            char_count = #{charCount},
            update_time = now()
        where id = #{id}
    </update>

    <update id="softDeleteDoc" parameterType="Long">
        update a_knowledge_doc
        set delete_time = now()
        where id = #{id}
    </update>

</mapper> 