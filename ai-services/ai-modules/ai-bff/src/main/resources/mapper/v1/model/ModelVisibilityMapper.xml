<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelVisibilityMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.model.ModelVisibility" id="ModelVisibilityResult">
        <result property="id" column="id"/>
        <result property="modelId" column="model_id"/>
        <result property="visibilityType" column="visibility_type"/>
        <result property="referenceId" column="reference_id"/>
        <result property="isEnabled" column="is_enabled"/>
        <result property="description" column="description"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectModelVisibilityVo">
        select id, model_id, visibility_type, reference_id, is_enabled, description,
               delete_time, create_time, update_time
        from a_model_visibility
    </sql>

    <select id="selectModelVisibilitiesByModelId" parameterType="Long" resultMap="ModelVisibilityResult">
        <include refid="selectModelVisibilityVo"/>
        where model_id = #{modelId} and is_enabled = true and delete_time is null
    </select>

</mapper> 