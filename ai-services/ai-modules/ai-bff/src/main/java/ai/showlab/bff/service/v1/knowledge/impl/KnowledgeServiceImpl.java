package ai.showlab.bff.service.v1.knowledge.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.entity.domain.v1.knowledge.Knowledge;
import ai.showlab.bff.entity.domain.v1.knowledge.KnowledgeDoc;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.KnowledgeBaseDetailVo;
import ai.showlab.bff.entity.vo.v1.KnowledgeBaseVo;
import ai.showlab.bff.entity.vo.v1.KnowledgeDocVo;
import ai.showlab.bff.mapper.v1.knowledge.KnowledgeDocMapper;
import ai.showlab.bff.mapper.v1.knowledge.KnowledgeMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.knowledge.IKnowledgeService;
import ai.showlab.bff.service.v1.model.IModelService;
import ai.showlab.common.core.constant.CacheConstants;
import com.ruoyi.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库服务实现类
 * <p>
 * 实现了知识库相关的业务逻辑，包括CRUD操作、文档管理等。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Slf4j
@Service
public class KnowledgeServiceImpl extends BaseService implements IKnowledgeService {

    @Autowired
    private KnowledgeMapper knowledgeMapper;

    @Autowired
    private KnowledgeDocMapper knowledgeDocMapper;

    @Autowired
    private IModelService modelService;

    @Override
    @CustomCache(value = CacheConstants.BFF_KNOWLEDGE_BASES_KEY,
            key = "#requestParams.bizParam != null ? " +
                    "T(ai.showlab.bff.common.util.BffKit).getCurrentMemberId() + ':' + " +
                    "(#requestParams.bizParam.keyword != null ? #requestParams.bizParam.keyword : 'all') + ':' + " +
                    "(#requestParams.bizParam.permissionType != null ? #requestParams.bizParam.permissionType : 'all') : " +
                    "T(ai.showlab.bff.common.util.BffKit).getCurrentMemberId() + ':all:all'")
    public List<KnowledgeBaseVo> getMemberKnowledgeBases(RequestParams<KnowledgeListParam> requestParams) {
        try {
            Long memberId = BffKit.getCurrentMemberId();
            KnowledgeListParam param = requestParams.getBizParam();

            // 查询知识库列表
            List<Knowledge> knowledgeBases = knowledgeMapper.selectKnowledgesByMemberId(memberId);

            // 过滤和转换
            return knowledgeBases.stream()
                    .filter(k -> filterByKeyword(k, param != null ? param.getKeyword() : null))
                    .filter(k -> filterByPermissionType(k, param != null ? param.getPermissionType() : null))
                    .map(this::convertToVo)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取会员知识库列表失败, memberId: {}", BffKit.getCurrentMemberId(), e);
            throw new BusinessException("获取知识库列表失败");
        }
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_KNOWLEDGE_BASE_DETAIL_KEY, key = "#requestParams.bizParam.knowledgeId")
    public KnowledgeBaseDetailVo getKnowledgeBaseDetail(RequestParams<KnowledgeDetailParam> requestParams) {
        try {
            Long memberId = BffKit.getCurrentMemberId();
            Long knowledgeId = requestParams.getBizParam().getKnowledgeId();

            Knowledge knowledge = getAndCheckOwner(knowledgeId, memberId);

            KnowledgeBaseDetailVo vo = new KnowledgeBaseDetailVo();
            BeanUtils.copyProperties(knowledge, vo);

            // 获取嵌入模型名称
            if (knowledge.getEmbeddingModelId() != null) {
                try {
                    // TODO: 获取模型名称的逻辑
                    vo.setEmbeddingModelName("模型名称待实现");
                } catch (Exception e) {
                    log.warn("获取嵌入模型名称失败, modelId: {}", knowledge.getEmbeddingModelId(), e);
                }
            }

            // 获取文档列表并转换为VO
            List<KnowledgeDoc> docs = knowledgeDocMapper.selectDocsByKnowledgeId(knowledgeId);
            List<KnowledgeDocVo> docVos = docs.stream()
                    .map(this::convertDocToVo)
                    .collect(Collectors.toList());
            vo.setDocuments(docVos);

            return vo;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取知识库详情失败, knowledgeId: {}", requestParams.getBizParam().getKnowledgeId(), e);
            throw new BusinessException("获取知识库详情失败");
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_KNOWLEDGE_BASES_KEY, allEntries = true)
    public KnowledgeBaseVo createKnowledgeBase(RequestParams<KnowledgeCreateParam> requestParams) {
        try {
            Long memberId = BffKit.getCurrentMemberId();
            KnowledgeCreateParam param = requestParams.getBizParam();

            // 验证嵌入模型是否存在
            // TODO: 验证模型存在性的逻辑

            Knowledge knowledge = new Knowledge();
            BeanUtils.copyProperties(param, knowledge);
            knowledge.setMemberId(memberId);

            // 设置默认值
            if (knowledge.getSortOrder() == null) {
                knowledge.setSortOrder(0);
            }
            if (knowledge.getPermissionType() == null) {
                // 默认私有
                knowledge.setPermissionType(1);
            }

            knowledgeMapper.insertKnowledge(knowledge);

            return convertToVo(knowledge);
        } catch (Exception e) {
            log.error("创建知识库失败, memberId: {}", BffKit.getCurrentMemberId(), e);
            throw new BusinessException("创建知识库失败");
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(value = CacheConstants.BFF_KNOWLEDGE_BASES_KEY, allEntries = true),
            @CacheEvict(value = CacheConstants.BFF_KNOWLEDGE_BASE_DETAIL_KEY, key = "#requestParams.bizParam.knowledgeId")
    })
    public void updateKnowledgeBase(RequestParams<KnowledgeUpdateParam> requestParams) {
        try {
            Long memberId = BffKit.getCurrentMemberId();
            KnowledgeUpdateParam param = requestParams.getBizParam();
            Long knowledgeId = param.getKnowledgeId();

            getAndCheckOwner(knowledgeId, memberId);

            Knowledge knowledgeToUpdate = new Knowledge();
            BeanUtils.copyProperties(param, knowledgeToUpdate);
            knowledgeToUpdate.setId(knowledgeId);
            knowledgeToUpdate.setMemberId(memberId);

            knowledgeMapper.updateKnowledge(knowledgeToUpdate);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新知识库失败, knowledgeId: {}", requestParams.getBizParam().getKnowledgeId(), e);
            throw new BusinessException("更新知识库失败");
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
            @CacheEvict(value = CacheConstants.BFF_KNOWLEDGE_BASES_KEY, allEntries = true),
            @CacheEvict(value = CacheConstants.BFF_KNOWLEDGE_BASE_DETAIL_KEY, key = "#requestParams.bizParam.knowledgeId")
    })
    public void deleteKnowledgeBase(RequestParams<KnowledgeDeleteParam> requestParams) {
        try {
            Long memberId = BffKit.getCurrentMemberId();
            Long knowledgeId = requestParams.getBizParam().getKnowledgeId();

            getAndCheckOwner(knowledgeId, memberId);
            knowledgeMapper.softDeleteKnowledge(knowledgeId, memberId);

            // TODO: 触发异步事件，删除向量数据库中的相关数据
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除知识库失败, knowledgeId: {}", requestParams.getBizParam().getKnowledgeId(), e);
            throw new BusinessException("删除知识库失败");
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_KNOWLEDGE_BASE_DETAIL_KEY, key = "#requestParams.bizParam.knowledgeId")
    public KnowledgeDocVo uploadDocument(RequestParams<KnowledgeDocUploadParam> requestParams) {
        try {
            Long memberId = BffKit.getCurrentMemberId();
            KnowledgeDocUploadParam param = requestParams.getBizParam();
            Long knowledgeId = param.getKnowledgeId();

            getAndCheckOwner(knowledgeId, memberId);

            KnowledgeDoc doc = new KnowledgeDoc();
            BeanUtils.copyProperties(param, doc);
            // 待处理状态
            doc.setProcessingStatus(2);
            doc.setChunkCount(0);
            doc.setCharCount(0L);

            knowledgeDocMapper.insertDoc(doc);

            // TODO: 触发异步事件或调用MQ，通知后台任务开始处理该文档

            return convertDocToVo(doc);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("上传文档失败, knowledgeId: {}", requestParams.getBizParam().getKnowledgeId(), e);
            throw new BusinessException("上传文档失败");
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_KNOWLEDGE_BASE_DETAIL_KEY, key = "#requestParams.bizParam.knowledgeId")
    public void deleteDocument(RequestParams<KnowledgeDocDeleteParam> requestParams) {
        try {
            Long memberId = BffKit.getCurrentMemberId();
            KnowledgeDocDeleteParam param = requestParams.getBizParam();
            Long knowledgeId = param.getKnowledgeId();
            Long docId = param.getDocId();

            getAndCheckOwner(knowledgeId, memberId);
            knowledgeDocMapper.softDeleteDoc(docId, knowledgeId);

            // TODO: 触发异步事件，通知后台从向量数据库中删除相关chunks
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除文档失败, docId: {}, knowledgeId: {}",
                    requestParams.getBizParam().getDocId(),
                    requestParams.getBizParam().getKnowledgeId(), e);
            throw new BusinessException("删除文档失败");
        }
    }

    /**
     * 获取知识库并检查所有者权限
     *
     * @param knowledgeId 知识库ID
     * @param memberId    会员ID
     * @return 知识库信息
     * @throws BusinessException 如果知识库不存在或无权访问
     */
    private Knowledge getAndCheckOwner(Long knowledgeId, Long memberId) {
        Knowledge knowledge = knowledgeMapper.selectKnowledgeById(knowledgeId);
        if (knowledge == null || !knowledge.getMemberId().equals(memberId)) {
            throw new BusinessException("知识库不存在或无权访问");
        }
        return knowledge;
    }

    /**
     * 按关键词过滤知识库
     *
     * @param knowledge 知识库
     * @param keyword   关键词
     * @return 是否匹配
     */
    private boolean filterByKeyword(Knowledge knowledge, String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return true;
        }
        return StringUtils.containsIgnoreCase(knowledge.getName(), keyword) ||
                StringUtils.containsIgnoreCase(knowledge.getDescription(), keyword);
    }

    /**
     * 按权限类型过滤知识库
     *
     * @param knowledge      知识库
     * @param permissionType 权限类型
     * @return 是否匹配
     */
    private boolean filterByPermissionType(Knowledge knowledge, Integer permissionType) {
        if (permissionType == null) {
            return true;
        }
        return knowledge.getPermissionType().equals(permissionType);
    }

    /**
     * 将Knowledge实体转换为VO
     *
     * @param knowledge 知识库实体
     * @return 知识库VO
     */
    private KnowledgeBaseVo convertToVo(Knowledge knowledge) {
        KnowledgeBaseVo vo = new KnowledgeBaseVo();
        BeanUtils.copyProperties(knowledge, vo);

        // TODO: 设置统计信息（文档数量、总字符数等）
        vo.setDocCount(0);
        vo.setTotalCharCount(0L);

        return vo;
    }

    /**
     * 将KnowledgeDoc实体转换为VO
     *
     * @param doc 文档实体
     * @return 文档VO
     */
    private KnowledgeDocVo convertDocToVo(KnowledgeDoc doc) {
        KnowledgeDocVo vo = new KnowledgeDocVo();
        BeanUtils.copyProperties(doc, vo);
        return vo;
    }
} 