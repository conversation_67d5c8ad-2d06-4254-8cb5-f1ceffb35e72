package ai.showlab.bff.service.v1.member;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.domain.v1.member.MemberAssistant;
import ai.showlab.bff.entity.dto.v1.MemberAssistantCreateDto;
import ai.showlab.bff.entity.dto.v1.MemberAssistantUpdateDto;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.common.core.web.page.PageResult;

import java.util.List;

/**
 * 会员助手实例服务接口
 * <p>
 * 定义了面向外部用户的会员助手实例相关业务逻辑。
 * 包括助手实例的CRUD操作、个性化配置等功能。
 * 兼容原有接口，同时扩展新的功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IMemberAssistantService {

    // ==================== 原有接口（保持兼容性） ====================

    /**
     * 获取会员的所有AI助手列表
     * <p>
     * 此方法会进行缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @return 助手列表
     */
    List<MemberAssistant> getAssistantsByMemberId(Long memberId);

    /**
     * 获取会员的所有活动的AI助手列表
     *
     * @param memberId 会员ID
     * @return 活动的助手列表
     */
    List<MemberAssistant> getActiveAssistantsByMemberId(Long memberId);

    /**
     * 获取会员收藏的AI助手列表
     *
     * @param memberId 会员ID
     * @return 收藏的助手列表
     */
    List<MemberAssistant> getFavoriteAssistantsByMemberId(Long memberId);

    /**
     * 会员创建一个新的AI助手
     * <p>
     * 此方法会清除对应会员的助手列表缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @param createDto 包含创建信息的DTO
     * @return 创建的助手对象
     */
    MemberAssistant createAssistant(Long memberId, MemberAssistantCreateDto createDto);

    /**
     * 更新会员的一个AI助手
     * <p>
     * 此方法会清除对应会员的助手列表缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @param assistantId 要更新的助手ID
     * @param updateDto 包含更新信息的DTO
     * @return 更新后的助手对象
     */
    MemberAssistant updateAssistant(Long memberId, Long assistantId, MemberAssistantUpdateDto updateDto);

    /**
     * 删除会员的一个AI助手
     * <p>
     * 此方法会清除对应会员的助手列表缓存。
     * </p>
     *
     * @param memberId    会员ID
     * @param assistantId 要删除的助手ID
     */
    void deleteAssistant(Long memberId, Long assistantId);

    // ==================== 新增接口（面向外部API） ====================

    /**
     * 获取当前会员的助手实例列表
     * <p>
     * 支持按关键词搜索、收藏状态、激活状态等条件过滤。
     * 此方法会进行缓存以提高性能。
     * </p>
     *
     * @param requestParams 请求参数，包含搜索条件
     * @return 会员助手实例列表
     */
    PageResult<MemberAssistantVo> getMemberAssistantList(RequestParams<MemberAssistantListParam> requestParams);

    /**
     * 获取会员助手实例详情
     * <p>
     * 包含助手实例的完整配置信息、使用统计等。
     * 会校验用户权限，只能查看自己的助手实例。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID
     * @return 会员助手实例详细信息
     */
    MemberAssistantDetailVo getMemberAssistantDetail(RequestParams<MemberAssistantDetailParam> requestParams);

    /**
     * 创建会员助手实例
     * <p>
     * 基于助手模板为当前会员创建个性化的助手实例。
     * 会验证助手模板的可用性和会员权限。
     * </p>
     *
     * @param requestParams 请求参数，包含助手ID、模型ID等配置信息
     * @return 新创建的会员助手实例信息
     */
    MemberAssistantVo createMemberAssistant(RequestParams<MemberAssistantCreateParam> requestParams);

    /**
     * 更新会员助手实例配置
     * <p>
     * 允许会员修改助手实例的名称、参数配置、模型选择等。
     * 会校验用户权限，只能修改自己的助手实例。
     * </p>
     *
     * @param requestParams 请求参数，包含要更新的配置信息
     */
    void updateMemberAssistant(RequestParams<MemberAssistantUpdateParam> requestParams);

    /**
     * 删除会员助手实例
     * <p>
     * 软删除指定的会员助手实例。
     * 会校验用户权限，只能删除自己的助手实例。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID
     */
    void deleteMemberAssistant(RequestParams<MemberAssistantDeleteParam> requestParams);

    // ==================== 助手实例状态管理 ====================

    /**
     * 激活/停用会员助手实例
     * <p>
     * 切换助手实例的激活状态。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID和目标状态
     */
    void toggleMemberAssistantStatus(RequestParams<MemberAssistantToggleParam> requestParams);

    /**
     * 收藏/取消收藏会员助手实例
     * <p>
     * 切换助手实例的收藏状态。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID和目标状态
     */
    void toggleMemberAssistantFavorite(RequestParams<MemberAssistantToggleParam> requestParams);

    // ==================== 助手实例配置管理 ====================

    /**
     * 更新会员助手实例名称
     * <p>
     * 允许会员为助手实例设置个性化名称。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID和新名称
     */
    void updateMemberAssistantName(RequestParams<MemberAssistantUpdateNameParam> requestParams);

    /**
     * 更新会员助手实例模型
     * <p>
     * 允许会员为助手实例选择不同的AI模型。
     * 会验证模型的可用性和兼容性。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID和新模型ID
     */
    void updateMemberAssistantModel(RequestParams<MemberAssistantUpdateModelParam> requestParams);

    /**
     * 更新会员助手实例参数配置
     * <p>
     * 允许会员修改助手实例的参数配置，如唤醒词、响应风格等。
     * 会验证参数的有效性。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID和参数配置
     */
    void updateMemberAssistantSettings(RequestParams<MemberAssistantUpdateSettingsParam> requestParams);

    // ==================== 助手实例查询功能 ====================

    /**
     * 获取会员的激活助手实例列表
     * <p>
     * 返回当前会员所有激活状态的助手实例，用于快速访问。
     * </p>
     *
     * @return 激活的助手实例列表
     */
    List<MemberAssistantVo> getActiveMemberAssistants();

    /**
     * 获取会员的收藏助手实例列表
     * <p>
     * 返回当前会员收藏的助手实例列表。
     * </p>
     *
     * @return 收藏的助手实例列表
     */
    List<MemberAssistantVo> getFavoriteMemberAssistants();

    /**
     * 检查会员是否已创建指定助手的实例
     * <p>
     * 用于判断会员是否已经添加了某个助手模板。
     * </p>
     *
     * @param assistantId 助手模板ID
     * @return 是否已创建实例
     */
    boolean hasMemberAssistantInstance(Long assistantId);

    /**
     * 获取会员助手实例的使用统计
     * <p>
     * 返回指定助手实例的使用统计信息。
     * </p>
     *
     * @param requestParams 请求参数，包含助手实例ID
     * @return 使用统计信息
     */
    MemberAssistantUsageStatsVo getMemberAssistantUsageStats(RequestParams<MemberAssistantDetailParam> requestParams);

    // ==================== 内部方法 ====================

    /**
     * 根据ID获取会员助手实例（内部方法）
     * <p>
     * 此方法会进行缓存，主要用于内部业务逻辑。
     * </p>
     *
     * @param memberAssistantId 会员助手实例ID
     * @return 会员助手实例
     */
    MemberAssistant getMemberAssistantById(Long memberAssistantId);

    /**
     * 验证会员助手实例所有权（内部方法）
     * <p>
     * 验证指定的助手实例是否属于当前会员。
     * </p>
     *
     * @param memberAssistantId 会员助手实例ID
     * @param memberId 会员ID
     * @return 会员助手实例（如果验证通过）
     * @throws ai.showlab.bff.common.exception.BusinessException 如果验证失败
     */
    MemberAssistant validateMemberAssistantOwnership(Long memberAssistantId, Long memberId);
}