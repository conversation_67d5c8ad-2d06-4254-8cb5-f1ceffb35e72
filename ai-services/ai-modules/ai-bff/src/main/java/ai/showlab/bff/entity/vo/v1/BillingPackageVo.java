package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 计费套餐视图对象
 * <p>
 * 用于向前端展示套餐信息，包含关联的货币信息。
 * </p>
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingPackageVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 套餐唯一标识
     */
    private String code;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐详细描述
     */
    private String description;

    /**
     * 套餐类型 (字典: package_type), 1-一次性, 2-订阅
     */
    private Integer type;
    
    /**
     * 套餐类型描述
     */
    private String typeDesc;

    /**
     * 套餐标价
     */
    private BigDecimal price;

    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;
    
    /**
     * 货币代码 (如 CNY, USD)
     */
    private String currencyCode;

    /**
     * 购买后授予的点数/积分/Token数量
     */
    private BigDecimal creditsGranted;

    /**
     * 授予的点数/积分的有效期（天），NULL表示永久
     */
    private Integer validityDays;

    /**
     * (针对订阅类型) 续订周期单位描述
     */
    private String renewalIntervalDesc;

    /**
     * 购买此套餐后授予的会员等级描述
     */
    private String memberLevelDesc;

    /**
     * 套餐状态 (字典: package_status), 1-草稿, 2-上架, 3-下架
     */
    private Integer status;
    
    /**
     * 套餐状态描述
     */
    private String statusDesc;

    /**
     * 套餐在前端的展示排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
}
