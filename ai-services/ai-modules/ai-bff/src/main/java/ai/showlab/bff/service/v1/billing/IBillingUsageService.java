package ai.showlab.bff.service.v1.billing;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.dto.v1.BillingUsageDto;
import ai.showlab.bff.entity.param.BillingUsageListParam;
import ai.showlab.bff.entity.vo.v1.BillingUsageVo;
import ai.showlab.common.core.web.page.PageResult;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 计费使用记录服务接口
 * <p>
 * 定义了面向外部用户的使用记录相关业务逻辑。
 * 包括使用记录查询、统计分析等功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IBillingUsageService {

    // ==================== 使用记录查询功能 ====================

    /**
     * 分页查询当前会员的使用记录
     * <p>
     * 支持按模型、时间范围等条件筛选，返回适合前端展示的VO对象。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 分页结果
     */
    PageResult<BillingUsageVo> getMemberUsageList(RequestParams<BillingUsageListParam> requestParams);

    /**
     * 根据ID获取使用记录详情
     * <p>
     * 返回使用记录的详细信息。
     * 只能查询当前会员的使用记录。
     * </p>
     *
     * @param usageId 使用记录ID
     * @return 使用记录详情VO
     */
    BillingUsageVo getUsageDetail(Long usageId);

    // ==================== 使用统计功能 ====================

    /**
     * 获取当前会员的使用量统计
     * <p>
     * 统计指定时间范围内的总使用量。
     * </p>
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param modelId   模型ID（可选）
     * @return 总使用量
     */
    BigDecimal getMemberUsageTotal(OffsetDateTime startTime, OffsetDateTime endTime, Long modelId);

    /**
     * 获取当前会员的使用次数统计
     * <p>
     * 统计指定时间范围内的总使用次数。
     * </p>
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param modelId   模型ID（可选）
     * @return 总使用次数
     */
    Long getMemberUsageCount(OffsetDateTime startTime, OffsetDateTime endTime, Long modelId);

    /**
     * 获取当前会员本月的使用统计
     * <p>
     * 统计当前月份的使用量和次数。
     * </p>
     *
     * @return 使用统计信息
     */
    BillingUsageVo getCurrentMonthUsageStats();

    /**
     * 获取当前会员今日的使用统计
     * <p>
     * 统计今日的使用量和次数。
     * </p>
     *
     * @return 使用统计信息
     */
    BillingUsageVo getTodayUsageStats();

    /**
     * 获取当前会员最常用的模型列表
     * <p>
     * 根据使用频次排序，返回最常用的模型。
     * </p>
     *
     * @param limit 返回数量限制
     * @return 常用模型列表
     */
    java.util.List<BillingUsageVo> getMostUsedModels(int limit);

    // ==================== 使用记录创建功能 ====================

    /**
     * 记录模型使用
     * <p>
     * 记录一次模型调用的使用情况，包括计费和扣费。
     * 这是核心的计费方法。
     * </p>
     *
     * @param usageDto 使用记录数据
     * @return 是否记录成功
     */
    boolean recordUsage(BillingUsageDto usageDto);

    // ==================== 内部方法 ====================

    /**
     * 检查使用记录是否属于指定会员
     * <p>
     * 用于权限验证。
     * </p>
     *
     * @param usageId  使用记录ID
     * @param memberId 会员ID
     * @return 是否属于该会员
     */
    boolean isUsageBelongToMember(Long usageId, Long memberId);

    /**
     * 计算使用费用
     * <p>
     * 根据模型、会员等级、使用量计算费用。
     * </p>
     *
     * @param modelId    模型ID
     * @param memberId   会员ID
     * @param amount     使用量
     * @return 费用金额
     */
    BigDecimal calculateUsageCost(Long modelId, Long memberId, BigDecimal amount);
}
