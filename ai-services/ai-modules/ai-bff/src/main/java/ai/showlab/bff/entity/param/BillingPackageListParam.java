package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 计费套餐列表查询参数
 * <p>
 * 用于查询可用的计费套餐，支持按类型、价格范围等条件筛选。
 * </p>
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class BillingPackageListParam {
    
    /**
     * 套餐类型，可选 (字典: package_type), 1-一次性, 2-订阅
     */
    private Integer type;
    
    /**
     * 货币ID，可选
     */
    private Long currencyId;
    
    /**
     * 最小价格，可选
     */
    private java.math.BigDecimal minPrice;
    
    /**
     * 最大价格，可选
     */
    private java.math.BigDecimal maxPrice;
    
    /**
     * 关键词搜索，可选（搜索套餐名称和描述）
     */
    private String keyword;
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
