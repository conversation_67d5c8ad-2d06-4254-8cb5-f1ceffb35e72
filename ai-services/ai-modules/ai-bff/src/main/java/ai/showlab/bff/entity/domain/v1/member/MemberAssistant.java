package ai.showlab.bff.entity.domain.v1.member;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.OffsetDateTime;

/**
 * 会员助手实例实体类
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(of = "id")
public class MemberAssistant implements Serializable {
    /**
     * 序列化版本ID
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 会员ID，关联 a_member.id
     */
    private Long memberId;
    
    /**
     * 助手ID，关联 a_assistant.id，表示实例化的哪个助手模板
     */
    private Long assistantId;
    
    /**
     * 会员添加此助手时，其模板的版本号
     */
    private String templateVersion;
    
    /**
     * 会员为此助手实例选择的具体模型ID，关联 a_model.id
     */
    private Long modelId;
    
    /**
     * 助手实例呢称，默认由系统自动生成
     */
    private String customName;
    
    /**
     * 会员覆盖的参数值 (JSON)，以 key-value 形式存储，如 {"wake_word": "你好AI"}
     */
    private String settingsOverride;
    
    /**
     * 是否收藏
     */
    private Boolean isFavorite;
    
    /**
     * 是否在会员的助手列表中启用
     */
    private Boolean isActive;

    /**
     * 使用次数统计
     */
    private Long usageCount;

    /**
     * 最后使用时间
     */
    private OffsetDateTime lastUsedTime;

    /**
     * 软删除标记时间
     */
    private OffsetDateTime deleteTime;
    
    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
    
    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
} 