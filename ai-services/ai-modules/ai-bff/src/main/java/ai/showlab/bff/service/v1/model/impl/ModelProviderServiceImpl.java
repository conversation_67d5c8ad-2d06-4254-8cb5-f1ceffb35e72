package ai.showlab.bff.service.v1.model.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.model.ModelProvider;
import ai.showlab.bff.mapper.v1.model.ModelProviderMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.model.IModelProviderService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型供应商服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class ModelProviderServiceImpl extends BaseService implements IModelProviderService {

    @Autowired
    private ModelProviderMapper modelProviderMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODEL_PROVIDERS_KEY, key = "'all'")
    public List<ModelProvider> getAllProviders() {
        return modelProviderMapper.selectAllModelProviders();
    }
} 