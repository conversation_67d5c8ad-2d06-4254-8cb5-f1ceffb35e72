package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiAuth;
import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.docs.BillingApiAnnotations;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.ParamValidateUtil;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.bff.service.v1.billing.*;
import ai.showlab.common.core.web.domain.RestResult;
import ai.showlab.common.core.web.page.PageResult;
import ai.showlab.common.protocol.enums.ApiAuthTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计费接口 (面向外部普通用户)
 * <p>
 * 提供计费相关的功能，包括套餐查询、订单管理、余额查询、交易记录、使用记录等。
 * </p>
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/billing")
@Tag(name = "计费接口", description = "提供计费相关的功能，包括套餐查询、订单管理、余额查询、交易记录、使用记录等，面向普通会员使用。")
public class BillingController extends BaseController {

    @Autowired
    private IBillingPackageService packageService;
    
    @Autowired
    private IBillingOrderService orderService;
    
    @Autowired
    private IBillingBalanceService balanceService;
    
    @Autowired
    private IBillingTransactionService transactionService;
    
    @Autowired
    private IBillingUsageService usageService;

    // ==================== 套餐相关接口 ====================

    /**
     * 获取计费套餐列表
     * @param requestParams 查询参数
     * @return 套餐列表
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @BillingApiAnnotations.GetPackageListApiDoc()
    @ApiParamValidate(bizParamClass = BillingPackageListParam.class)
    @PostMapping("/package/list")
    public ResponseEntity<RestResult> getPackageList(RequestParams<BillingPackageListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取套餐列表
            PageResult<BillingPackageVo> result = packageService.getPackageList(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取套餐列表失败，请稍后重试");
    }

    /**
     * 获取套餐详情
     * @param requestParams 查询参数
     * @return 套餐详情
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @BillingApiAnnotations.GetPackageDetailApiDoc()
    @ApiParamValidate(bizParamClass = BillingPackageDetailParam.class)
    @PostMapping("/package/detail")
    public ResponseEntity<RestResult> getPackageDetail(RequestParams<BillingPackageDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取套餐详情
            BillingPackageVo packageVo = packageService.getPackageDetail(requestParams);
            return checkNotNull(packageVo, "套餐不存在或已下线");
        }, "获取套餐详情失败，请稍后重试");
    }

    /**
     * 获取热门套餐列表
     * @return 热门套餐列表
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @BillingApiAnnotations.GetPopularPackagesApiDoc()
    @GetMapping("/package/popular")
    public ResponseEntity<RestResult> getPopularPackages() {
        return executeWithTryCatch(() -> {
            List<BillingPackageVo> packages = packageService.getPopularPackages();
            return RestResult.ok("获取成功", packages);
        }, "获取热门套餐失败，请稍后重试");
    }

    /**
     * 获取推荐套餐列表
     * @return 推荐套餐列表
     */
    @BillingApiAnnotations.GetRecommendedPackagesApiDoc()
    @GetMapping("/package/recommended")
    public ResponseEntity<RestResult> getRecommendedPackages() {
        return executeWithTryCatch(() -> {
            List<BillingPackageVo> packages = packageService.getRecommendedPackages();
            return RestResult.ok("获取成功", packages);
        }, "获取推荐套餐失败，请稍后重试");
    }

    // ==================== 订单相关接口 ====================

    /**
     * 创建订单
     * @param requestParams 创建参数
     * @return 订单信息
     */
    @BillingApiAnnotations.CreateOrderApiDoc()
    @ApiParamValidate(bizParamClass = BillingOrderCreateParam.class)
    @PostMapping("/order/create")
    public ResponseEntity<RestResult> createOrder(RequestParams<BillingOrderCreateParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务创建订单
            BillingOrderVo order = orderService.createOrder(requestParams);
            return RestResult.ok("订单创建成功", order);
        }, "创建订单失败，请稍后重试");
    }

    /**
     * 获取我的订单列表
     * @param requestParams 查询参数
     * @return 订单列表
     */
    @BillingApiAnnotations.GetOrderListApiDoc()
    @ApiParamValidate(bizParamClass = BillingOrderListParam.class)
    @PostMapping("/order/list")
    public ResponseEntity<RestResult> getOrderList(RequestParams<BillingOrderListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取订单列表
            PageResult<BillingOrderVo> result = orderService.getMemberOrderList(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取订单列表失败，请稍后重试");
    }

    /**
     * 获取订单详情
     * @param orderNo 订单号
     * @return 订单详情
     */
    @BillingApiAnnotations.GetOrderDetailApiDoc()
    @GetMapping("/order/detail/{orderNo}")
    public ResponseEntity<RestResult> getOrderDetail(@PathVariable String orderNo) {
        return executeWithTryCatch(() -> {
            if (StringUtils.isBlank(orderNo)) {
                throw new BusinessException("订单号不能为空");
            }
            // 调用服务获取订单详情
            BillingOrderVo order = orderService.getOrderDetail(orderNo);
            return checkNotNull(order, "订单不存在或无权访问");
        }, "获取订单详情失败，请稍后重试");
    }

    /**
     * 取消订单
     * @param orderNo 订单号
     * @return 取消结果
     */
    @BillingApiAnnotations.CancelOrderApiDoc()
    @PostMapping("/order/cancel/{orderNo}")
    public ResponseEntity<RestResult> cancelOrder(@PathVariable String orderNo) {
        return executeWithTryCatch(() -> {
            if (StringUtils.isBlank(orderNo)) {
                throw new BusinessException("订单号不能为空");
            }
            // 调用服务取消订单
            boolean success = orderService.cancelOrder(orderNo);
            return success ? RestResult.ok("订单取消成功") : RestResult.error("订单取消失败，请检查订单状态");
        }, "取消订单失败，请稍后重试");
    }

    // ==================== 余额相关接口 ====================

    /**
     * 获取我的余额信息
     * @return 余额信息
     */
    @BillingApiAnnotations.GetBalanceApiDoc()
    @GetMapping("/balance")
    public ResponseEntity<RestResult> getBalance() {
        return executeWithTryCatch(() -> {
            BillingBalanceVo balance = balanceService.getCurrentMemberBalance();
            return checkNotNull(balance, "余额信息不存在");
        }, "获取余额信息失败，请稍后重试");
    }

    // ==================== 交易记录相关接口 ====================

    /**
     * 获取我的交易记录
     * @param requestParams 查询参数
     * @return 交易记录列表
     */
    @BillingApiAnnotations.GetTransactionListApiDoc()
    @ApiParamValidate(bizParamClass = BillingTransactionListParam.class)
    @PostMapping("/transaction/list")
    public ResponseEntity<RestResult> getTransactionList(RequestParams<BillingTransactionListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取交易记录列表
            PageResult<BillingTransactionVo> result = transactionService.getMemberTransactionList(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取交易记录失败，请稍后重试");
    }

    /**
     * 获取交易记录详情
     * @param transactionId 交易记录ID
     * @return 交易记录详情
     */
    @BillingApiAnnotations.GetTransactionDetailApiDoc()
    @GetMapping("/transaction/detail/{transactionId}")
    public ResponseEntity<RestResult> getTransactionDetail(@PathVariable Long transactionId) {
        return executeWithTryCatch(() -> {
            if (transactionId == null || transactionId <= 0) {
                throw new BusinessException("交易记录ID无效");
            }
            // 调用服务获取交易记录详情
            BillingTransactionVo transaction = transactionService.getTransactionDetail(transactionId);
            return checkNotNull(transaction, "交易记录不存在或无权访问");
        }, "获取交易记录详情失败，请稍后重试");
    }

    // ==================== 使用记录相关接口 ====================

    /**
     * 获取我的使用记录
     * @param requestParams 查询参数
     * @return 使用记录列表
     */
    @BillingApiAnnotations.GetUsageListApiDoc()
    @ApiParamValidate(bizParamClass = BillingUsageListParam.class)
    @PostMapping("/usage/list")
    public ResponseEntity<RestResult> getUsageList(RequestParams<BillingUsageListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取使用记录列表
            PageResult<BillingUsageVo> result = usageService.getMemberUsageList(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取使用记录失败，请稍后重试");
    }

    /**
     * 获取使用记录详情
     * @param usageId 使用记录ID
     * @return 使用记录详情
     */
    @BillingApiAnnotations.GetUsageDetailApiDoc()
    @GetMapping("/usage/detail/{usageId}")
    public ResponseEntity<RestResult> getUsageDetail(@PathVariable Long usageId) {
        return executeWithTryCatch(() -> {
            if (usageId == null || usageId <= 0) {
                throw new BusinessException("使用记录ID无效");
            }
            // 调用服务获取使用记录详情
            BillingUsageVo usage = usageService.getUsageDetail(usageId);
            return checkNotNull(usage, "使用记录不存在或无权访问");
        }, "获取使用记录详情失败，请稍后重试");
    }

    /**
     * 获取我的使用统计
     * @return 使用统计信息
     */
    @BillingApiAnnotations.GetUsageStatsApiDoc()
    @GetMapping("/usage/stats")
    public ResponseEntity<RestResult> getUsageStats() {
        return executeWithTryCatch(() -> {
            // 获取本月和今日使用统计
            BillingUsageVo monthStats = usageService.getCurrentMonthUsageStats();
            BillingUsageVo todayStats = usageService.getTodayUsageStats();
            // 获取最常用的模型
            List<BillingUsageVo> mostUsedModels = usageService.getMostUsedModels(5);
            // 组装返回数据
            java.util.Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("monthStats", monthStats);
            stats.put("todayStats", todayStats);
            stats.put("mostUsedModels", mostUsedModels);
            return RestResult.ok("获取成功", stats);
        }, "获取使用统计失败，请稍后重试");
    }
}
