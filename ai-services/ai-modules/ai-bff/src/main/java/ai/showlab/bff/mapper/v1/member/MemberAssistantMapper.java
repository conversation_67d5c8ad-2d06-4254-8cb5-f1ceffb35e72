package ai.showlab.bff.mapper.v1.member;

import ai.showlab.bff.entity.domain.v1.member.MemberAssistant;
import ai.showlab.bff.entity.param.MemberAssistantListParam;
import ai.showlab.bff.entity.vo.v1.MemberAssistantDetailVo;
import ai.showlab.bff.entity.vo.v1.MemberAssistantVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员的AI助手 数据层
 * <p>
 * 管理会员拥有和定制的AI助手。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Repository
public interface MemberAssistantMapper {

    /**
     * 根据主键ID查询会员的助手
     *
     * @param id 主键ID
     * @return 会员助手对象
     */
    MemberAssistant selectMemberAssistantById(Long id);

    /**
     * 查询会员的所有助手
     *
     * @param memberId 会员ID
     * @return 会员助手列表
     */
    List<MemberAssistant> selectMemberAssistantsByMemberId(Long memberId);

    /**
     * 查询会员的所有活动助手
     *
     * @param memberId 会员ID
     * @return 活动状态的会员助手列表
     */
    List<MemberAssistant> selectActiveMemberAssistantsByMemberId(Long memberId);

    /**
     * 查询会员收藏的助手
     *
     * @param memberId 会员ID
     * @return 收藏状态的会员助手列表
     */
    List<MemberAssistant> selectFavoriteMemberAssistantsByMemberId(Long memberId);

    /**
     * 为会员添加一个新的助手
     *
     * @param memberAssistant 助手信息
     * @return 影响的行数
     */
    int insertMemberAssistant(MemberAssistant memberAssistant);

    /**
     * 更新会员的助手信息（如修改自定义名称、设置等）
     *
     * @param memberAssistant 包含更新信息的助手对象
     * @return 影响的行数
     */
    int updateMemberAssistant(MemberAssistant memberAssistant);

    /**
     * 软删除会员的助手
     *
     * @param id       助手记录的主键ID
     * @param memberId 会员ID，用于安全校验
     * @return 影响的行数
     */
    int softDeleteMemberAssistant(@Param("id") Long id, @Param("memberId") Long memberId);

    // ==================== 新增方法（面向外部API） ====================

    /**
     * 分页查询会员助手实例列表（返回VO对象）
     *
     * @param param 查询参数
     * @param memberId 会员ID
     * @return 会员助手实例列表VO
     */
    List<MemberAssistantVo> selectMemberAssistantListVo(@Param("param") MemberAssistantListParam param, @Param("memberId") Long memberId);

    /**
     * 统计会员助手实例数量
     *
     * @param param 查询参数
     * @param memberId 会员ID
     * @return 总数量
     */
    long countMemberAssistants(@Param("param") MemberAssistantListParam param, @Param("memberId") Long memberId);

    /**
     * 根据ID查询会员助手实例详情（返回VO对象）
     *
     * @param id 会员助手实例ID
     * @param memberId 会员ID（用于权限校验）
     * @return 会员助手实例详情VO
     */
    MemberAssistantDetailVo selectMemberAssistantDetailVo(@Param("id") Long id, @Param("memberId") Long memberId);

    /**
     * 查询会员的激活助手实例列表（返回VO对象）
     *
     * @param memberId 会员ID
     * @return 激活的助手实例列表VO
     */
    List<MemberAssistantVo> selectActiveMemberAssistantsVo(Long memberId);

    /**
     * 查询会员的收藏助手实例列表（返回VO对象）
     *
     * @param memberId 会员ID
     * @return 收藏的助手实例列表VO
     */
    List<MemberAssistantVo> selectFavoriteMemberAssistantsVo(Long memberId);

    /**
     * 检查会员是否已创建指定助手的实例
     *
     * @param memberId 会员ID
     * @param assistantId 助手模板ID
     * @return 是否已创建实例
     */
    boolean existsMemberAssistantInstance(@Param("memberId") Long memberId, @Param("assistantId") Long assistantId);

    /**
     * 根据会员ID和助手ID查询会员助手实例
     *
     * @param memberId 会员ID
     * @param assistantId 助手模板ID
     * @return 会员助手实例
     */
    MemberAssistant selectMemberAssistantByMemberAndAssistant(@Param("memberId") Long memberId, @Param("assistantId") Long assistantId);

    /**
     * 更新会员助手实例状态
     *
     * @param id 会员助手实例ID
     * @param memberId 会员ID（用于权限校验）
     * @param isActive 是否激活
     * @return 影响的行数
     */
    int updateMemberAssistantStatus(@Param("id") Long id, @Param("memberId") Long memberId, @Param("isActive") Boolean isActive);

    /**
     * 更新会员助手实例收藏状态
     *
     * @param id 会员助手实例ID
     * @param memberId 会员ID（用于权限校验）
     * @param isFavorite 是否收藏
     * @return 影响的行数
     */
    int updateMemberAssistantFavorite(@Param("id") Long id, @Param("memberId") Long memberId, @Param("isFavorite") Boolean isFavorite);

    /**
     * 更新会员助手实例名称
     *
     * @param id 会员助手实例ID
     * @param memberId 会员ID（用于权限校验）
     * @param customName 新名称
     * @return 影响的行数
     */
    int updateMemberAssistantName(@Param("id") Long id, @Param("memberId") Long memberId, @Param("customName") String customName);

    /**
     * 更新会员助手实例模型
     *
     * @param id 会员助手实例ID
     * @param memberId 会员ID（用于权限校验）
     * @param modelId 新模型ID
     * @return 影响的行数
     */
    int updateMemberAssistantModel(@Param("id") Long id, @Param("memberId") Long memberId, @Param("modelId") Long modelId);

    /**
     * 更新会员助手实例参数配置
     *
     * @param id 会员助手实例ID
     * @param memberId 会员ID（用于权限校验）
     * @param settingsOverride 参数配置JSON
     * @return 影响的行数
     */
    int updateMemberAssistantSettings(@Param("id") Long id, @Param("memberId") Long memberId, @Param("settingsOverride") String settingsOverride);
}