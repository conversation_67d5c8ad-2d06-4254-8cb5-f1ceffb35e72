package ai.showlab.bff.service.v1.base.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.base.BaseLang;
import ai.showlab.bff.mapper.v1.base.BaseLangMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.base.IBaseLangService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 语言表 服务实现层
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class BaseLangServiceImpl extends BaseService implements IBaseLangService {

    @Autowired
    private BaseLangMapper langMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_LANG_KEY, key = "'all'")
    public List<BaseLang> getAllLangs() {
        return langMapper.selectAllLangs();
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_LANG_KEY, key = "#id")
    public BaseLang getLangById(Long id) {
        return langMapper.selectLangById(id);
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_LANG_KEY, key = "'default'")
    public BaseLang getDefaultLang() {
        return langMapper.selectDefaultLang();
    }
} 