package ai.showlab.bff.service.v1.base.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.base.BaseCountry;
import ai.showlab.bff.mapper.v1.base.BaseCountryMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.base.IBaseCountryService;
import ai.showlab.common.core.constant.CacheConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 国家表 服务实现层
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class BaseCountryServiceImpl extends BaseService implements IBaseCountryService {
    private static final Logger log = LoggerFactory.getLogger(BaseCountryServiceImpl.class);

    @Autowired
    private BaseCountryMapper countryMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_COUNTRY_KEY, key = "'all'")
    public List<BaseCountry> getAllCountries() {
        return countryMapper.selectAllCountries();
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_COUNTRY_KEY, key = "#id")
    public BaseCountry getCountryById(Long id) {
        return countryMapper.selectCountryById(id);
    }
} 