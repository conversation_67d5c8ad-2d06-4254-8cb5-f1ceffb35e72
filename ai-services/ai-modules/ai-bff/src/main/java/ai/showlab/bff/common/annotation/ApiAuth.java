package ai.showlab.bff.common.annotation;

import ai.showlab.common.protocol.enums.ApiAuthTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * API接口权限认证注解
 * <p>
 * 用于标记接口的访问权限级别和认证要求。
 * 如果接口没有添加此注解，则默认为LOGIN_REQUIRED（需要登录）。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiAuth {

    /**
     * 认证类型
     * <p>
     * 默认为LOGIN_REQUIRED，即需要登录后才能访问。
     * </p>
     *
     * @return 认证类型枚举
     */
    ApiAuthTypeEnum type() default ApiAuthTypeEnum.LOGIN_REQUIRED;

    /**
     * 权限编码
     * <p>
     * 当type为PERMISSION_REQUIRED时，此字段必须指定。
     * 对应a_func_permission表的code字段，用于权限校验。
     * </p>
     *
     * @return 权限编码
     */
    String code() default "";

    /**
     * 权限描述
     * <p>
     * 可选字段，用于描述该权限的作用，便于理解和维护。
     * </p>
     *
     * @return 权限描述
     */
    String description() default "";

    /**
     * 是否启用权限校验
     * <p>
     * 默认为true。如果设置为false，则跳过权限校验。
     * 主要用于开发和测试阶段临时禁用权限校验。
     * </p>
     *
     * @return 是否启用权限校验
     */
    boolean enabled() default true;
}
