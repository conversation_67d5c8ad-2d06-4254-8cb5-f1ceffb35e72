package ai.showlab.bff.service.v1.assistant;

import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.domain.v1.assistant.Assistant;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.common.core.web.page.PageResult;

import java.util.List;

/**
 * AI助手服务接口
 * <p>
 * 定义了面向外部用户的AI助手相关业务逻辑。
 * 包括助手的查询、分类、热门推荐等功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IAssistantService {

    // ==================== 助手查询功能 ====================

    /**
     * 分页查询助手列表
     * <p>
     * 支持按分类、关键词、交互模式等条件筛选，返回适合前端展示的VO对象。
     * 此方法会进行缓存以提高性能。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 分页结果
     * @throws BusinessException 业务异常
     */
    PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws BusinessException;

    /**
     * 根据ID获取助手详情
     * <p>
     * 返回包含完整信息的助手详情，包括参数配置、模型建议等。
     * 此方法会进行缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含助手ID
     * @return 助手详情VO
     */
    AssistantDetailVo getAssistantDetail(RequestParams<AssistantDetailParam> requestParams);

    /**
     * 根据编码获取助手信息
     * <p>
     * 用于助手调用前的信息获取，此方法会进行缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含助手编码
     * @return 助手详情VO
     */
    AssistantDetailVo getAssistantByCode(RequestParams<AssistantByCodeParam> requestParams);

    /**
     * 获取热门助手列表
     * <p>
     * 返回热门推荐的助手列表，用于首页展示。
     * 此方法会进行缓存。
     * </p>
     *
     * @return 热门助手列表
     */
    List<AssistantListVo> getPopularAssistants();

    /**
     * 获取公共AI助手列表
     * <p>
     * 此方法会缓存结果以提高性能。
     * </p>
     *
     * @return 公共AI助手列表
     */
    List<Assistant> getPublicAssistants();

    /**
     * 获取指定会员创建的AI助手列表
     *
     * @param memberId 会员ID
     * @return AI助手列表
     */
    List<Assistant> getMemberAssistants(Long memberId);

    /**
     * 获取AI助手详情（内部方法）
     * <p>
     * 详情信息会被缓存。
     * </p>
     *
     * @param assistantId AI助手ID
     * @return AI助手详细信息
     */
    Assistant getAssistantDetail(Long assistantId);

    // ==================== 会员权限相关方法 ====================

    /**
     * 获取当前会员可访问的助手列表
     * <p>
     * 根据会员等级、角色、地区等权限控制返回可访问的助手。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 可访问的助手分页结果
     */
    PageResult<AssistantListVo> getAccessibleAssistants(RequestParams<AssistantListParam> requestParams);

    /**
     * 检查当前会员是否有权限访问指定助手
     * <p>
     * 根据助手可见性规则验证会员权限。
     * </p>
     *
     * @param requestParams 请求参数，包含助手ID
     * @return 是否有访问权限
     */
    boolean checkAssistantAccess(RequestParams<AssistantDetailParam> requestParams);

    // ==================== 会员个性化功能 ====================

    /**
     * 获取当前会员收藏的助手列表
     * <p>
     * 返回会员收藏的助手，按收藏时间倒序排列。
     * </p>
     *
     * @return 收藏的助手列表
     */
    List<AssistantListVo> getFavoriteAssistants();

    /**
     * 收藏助手
     * <p>
     * 将指定助手添加到当前会员的收藏列表。
     * </p>
     *
     * @param requestParams 请求参数，包含助手ID
     */
    void favoriteAssistant(RequestParams<AssistantDetailParam> requestParams);

    /**
     * 取消收藏助手
     * <p>
     * 将指定助手从当前会员的收藏列表中移除。
     * </p>
     *
     * @param requestParams 请求参数，包含助手ID
     */
    void unfavoriteAssistant(RequestParams<AssistantDetailParam> requestParams);

    // ==================== 使用统计功能 ====================

    /**
     * 记录一次AI助手使用
     *
     * @param assistantId AI助手ID
     */
    void recordAssistantUsage(Long assistantId);

    /**
     * 获取当前会员的助手使用统计
     * <p>
     * 返回会员的总体使用统计，包括使用次数、使用时长等。
     * 委托给统计服务处理。
     * </p>
     *
     * @return 使用统计VO
     */
    MemberUsageStatsVo getMemberUsageStats();

    /**
     * 获取当前会员对指定助手的使用统计
     * <p>
     * 返回会员对特定助手的使用统计数据。
     * 委托给统计服务处理。
     * </p>
     *
     * @param requestParams 请求参数，包含助手ID
     * @return 助手使用统计VO
     */
    MemberAssistantUsageStatsVo getAssistantUsageStats(RequestParams<AssistantDetailParam> requestParams);

    /**
     * 获取所有可用的助手（内部方法）
     * <p>
     * 此方法会进行缓存，主要用于内部业务逻辑。
     * </p>
     *
     * @return 助手列表
     */
    List<Assistant> getAllAssistants();
}