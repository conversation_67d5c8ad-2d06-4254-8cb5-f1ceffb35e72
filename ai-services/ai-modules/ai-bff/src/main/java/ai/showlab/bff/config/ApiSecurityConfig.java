package ai.showlab.bff.config;

import ai.showlab.bff.common.constant.ApiUrlConstants;
import ai.showlab.bff.common.filter.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * API安全配置
 * <p>
 * 配置Spring Security的安全过滤链，主要负责JWT认证。
 * 具体的权限校验由ApiAuthInterceptor处理。
 * URL路径配置统一在ApiUrlConstants中管理。
 * </p>
 *
 * <h3>架构说明：</h3>
 * <ul>
 *   <li>Spring Security：负责JWT token解析和身份认证</li>
 *   <li>ApiAuthInterceptor：负责基于@ApiAuth注解的权限校验</li>
 *   <li>ApiUrlConstants：统一管理URL路径配置</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
public class ApiSecurityConfig {

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public SecurityFilterChain memberFilterChain(HttpSecurity http) throws Exception {
        // 禁用 CSRF
        http.csrf(AbstractHttpConfigurer::disable)
                // 无状态会话
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth -> auth
                        // 公共路径（不需要任何认证和权限校验）
                        .requestMatchers(ApiUrlConstants.PUBLIC_PATHS).permitAll()
                        // 认证排除路径（不需要JWT认证，但可能需要权限校验）
                        .requestMatchers(ApiUrlConstants.AUTH_EXCLUDED_PATHS).permitAll()
                        // 其他API接口需要JWT认证（具体权限由拦截器处理）
                        .requestMatchers(ApiUrlConstants.API_PATTERN).authenticated()
                        // 其他路径允许访问
                        .anyRequest().permitAll()
                )
                // 在 UsernamePasswordAuthenticationFilter 之前添加 JWT 过滤器
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
} 