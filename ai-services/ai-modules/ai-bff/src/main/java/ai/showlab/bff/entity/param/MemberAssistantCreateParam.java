package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 创建会员助手实例参数
 *
 * <AUTHOR>
 */
@Data
public class MemberAssistantCreateParam {

    /**
     * 助手模板ID
     */
    @NotNull(message = "助手ID不能为空")
    private Long assistantId;

    /**
     * 选择的模型ID
     */
    @NotNull(message = "模型ID不能为空")
    private Long modelId;

    /**
     * 自定义名称，可选（如果不提供，系统会自动生成）
     */
    private String customName;

    /**
     * 参数配置覆盖，可选
     * 以 key-value 形式存储，如 {"wake_word": "你好AI", "response_style": "formal"}
     */
    private Map<String, Object> settingsOverride;

    /**
     * 是否设为收藏，默认false
     */
    private Boolean isFavorite = false;

    /**
     * 是否激活，默认true
     */
    private Boolean isActive = true;
}
