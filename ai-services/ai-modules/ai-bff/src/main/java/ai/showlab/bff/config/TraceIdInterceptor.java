package ai.showlab.bff.config;

import ai.showlab.common.constants.BaseConstants;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * Interceptor to extract traceId from request headers, set it in MDC, and log request details.
 *
 * <AUTHOR>
 */
@Component
public class TraceIdInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(TraceIdInterceptor.class);
    // Define a max length for params to avoid overly long logs
    private static final int MAX_PARAM_LENGTH = 300;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        // Handle traceId
        String traceId = request.getHeader(BaseConstants.TRACE_ID_HEADER);
        if (traceId != null && !traceId.isEmpty()) {
            MDC.put(BaseConstants.REQ_ID_MDC_KEY, traceId);
        }

        // Log request parameters
        long startTime = System.currentTimeMillis();
        request.setAttribute(BaseConstants.REQUEST_START_TIME, startTime);

        String queryString = request.getQueryString();
        String requestBody = "";
        try {
            requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (Exception e) {
            log.warn("Failed to read request body: {}", e.getMessage());
        }

        Map<String, String[]> params = request.getParameterMap();
        String paramsString = params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + String.join(",", entry.getValue()))
                .collect(Collectors.joining("&"));

        String logMessage = String.format(" REQ: [%s]%s, Params: %s, Body: %s",
                request.getMethod(),
                request.getRequestURI(),
                truncateString(queryString != null ? queryString : "", MAX_PARAM_LENGTH),
                truncateString(requestBody, MAX_PARAM_LENGTH));

        log.info(logMessage);

        return true;
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, @Nullable ModelAndView modelAndView) {
        // Not used for this purpose
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, @Nullable Exception ex) {
        // Log request duration
        Long startTime = (Long) request.getAttribute(BaseConstants.REQUEST_START_TIME);
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            log.info("RES: {}, {}ms", request.getRequestURI(), duration);
        }

        MDC.remove(BaseConstants.REQ_ID_MDC_KEY);
    }

    private String truncateString(String str, int maxLength) {
        if (str == null) {
            return "";
        }
        return str.length() > maxLength ? str.substring(0, maxLength) + "..." : str;
    }
} 