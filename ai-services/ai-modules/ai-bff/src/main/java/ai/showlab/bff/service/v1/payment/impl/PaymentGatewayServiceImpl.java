package ai.showlab.bff.service.v1.payment.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.payment.PaymentGateway;
import ai.showlab.bff.entity.vo.v1.PaymentGatewayVo;
import ai.showlab.bff.mapper.v1.payment.PaymentGatewayMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.payment.IPaymentGatewayService;
import ai.showlab.common.core.constant.CacheConstants;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 支付网关 服务实现层
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Service
public class PaymentGatewayServiceImpl extends BaseService implements IPaymentGatewayService {

    @Autowired
    private PaymentGatewayMapper paymentGatewayMapper;

    /**
     * 根据ID获取支付网关的公开信息
     * 结果会被缓存。
     *
     * @param id 支付网关ID
     * @return 支付网关视图对象
     * @throws ServiceException 如果找不到支付网关
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_PAYMENT_GATEWAY_KEY, key = "#id", unless = "#result == null")
    public PaymentGatewayVo getPaymentGatewayById(Long id) {
        PaymentGateway gateway = paymentGatewayMapper.selectPaymentGatewayById(id);
        if (Objects.isNull(gateway)) {
            return null;
        }
        PaymentGatewayVo vo = new PaymentGatewayVo();
        BeanUtils.copyProperties(gateway, vo);
        return vo;
    }

    /**
     * 根据唯一编码获取支付网关的公开信息
     * 结果会被缓存。
     *
     * @param code 支付网关唯一编码
     * @return 支付网关视图对象
     * @throws ServiceException 如果找不到支付网关
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_PAYMENT_GATEWAY_KEY, key = "#code", unless = "#result == null")
    public PaymentGatewayVo getPaymentGatewayByCode(String code) {
        PaymentGateway gateway = paymentGatewayMapper.selectPaymentGatewayByCode(code);
        if (Objects.isNull(gateway)) {
            return null;
        }
        PaymentGatewayVo vo = new PaymentGatewayVo();
        BeanUtils.copyProperties(gateway, vo);
        return vo;
    }

    /**
     * 获取所有可用的支付网关列表
     * 结果会被缓存。
     *
     * @return 支付网关视图对象列表
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_PAYMENT_GATEWAY_KEY, key = "'available'")
    public List<PaymentGatewayVo> getAvailablePaymentGateways() {
        List<PaymentGateway> gateways = paymentGatewayMapper.selectAvailablePaymentGateways();
        if (CollectionUtils.isEmpty(gateways)) {
            return Collections.emptyList();
        }
        return gateways.stream().map(gateway -> {
            PaymentGatewayVo vo = new PaymentGatewayVo();
            BeanUtils.copyProperties(gateway, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据国家ID获取可用的支付网关列表
     * 结果会被缓存。
     *
     * @param countryId 国家ID
     * @return 支付网关视图对象列表
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_PAYMENT_GATEWAY_KEY, key = "'country:' + #countryId")
    public List<PaymentGatewayVo> getPaymentGatewaysByCountry(Long countryId) {
        if (Objects.isNull(countryId)) {
            throw new ServiceException("国家ID不能为空");
        }
        List<PaymentGateway> gateways = paymentGatewayMapper.selectPaymentGatewaysByCountry(countryId);
        if (CollectionUtils.isEmpty(gateways)) {
            return Collections.emptyList();
        }
        return gateways.stream().map(gateway -> {
            PaymentGatewayVo vo = new PaymentGatewayVo();
            BeanUtils.copyProperties(gateway, vo);
            return vo;
        }).collect(Collectors.toList());
    }
} 