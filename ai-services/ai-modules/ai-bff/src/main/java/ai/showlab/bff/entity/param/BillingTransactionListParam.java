package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 交易记录列表查询参数
 * <p>
 * 用于查询当前会员的交易流水记录，支持按类型、时间范围等条件筛选。
 * </p>
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class BillingTransactionListParam {
    
    /**
     * 交易类型，可选 (字典: transaction_type), 1-消费, 2-充值, 3-退款, 4-赠送, 5-分润
     */
    private Integer type;
    
    /**
     * 开始时间，可选
     */
    private OffsetDateTime startTime;
    
    /**
     * 结束时间，可选
     */
    private OffsetDateTime endTime;
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
