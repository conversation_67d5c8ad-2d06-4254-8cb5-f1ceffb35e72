package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.entity.domain.v1.member.MemberSession;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.member.IDuplicateLoginService;
import ai.showlab.bff.service.v1.member.IMemberSessionService;
import ai.showlab.common.constants.BaseConstants;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.core.constant.CodeConstants;
import com.ruoyi.common.core.utils.ip.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 重复登录处理服务实现类
 * 
 * <AUTHOR>
 * @date 2024-07-26
 */
@Slf4j
@Service
public class DuplicateLoginServiceImpl extends BaseService implements IDuplicateLoginService {
    
    @Autowired
    private IMemberSessionService memberSessionService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 重复登录处理策略，从配置文件读取
     * 默认值：KICK_OLD（新登录踢掉旧登录）
     */
    @Value("${app.security.duplicate-login.strategy:KICK_OLD}")
    private String duplicateLoginStrategy;
    
    /**
     * 最大同时登录设备数，从配置文件读取
     * 默认值：3
     */
    @Value("${app.security.duplicate-login.max-concurrent-sessions:3}")
    private int maxConcurrentSessions;
    
    /**
     * 是否启用同设备检查（同一设备的重复登录是否算作重复）
     * 默认值：false（同设备重复登录不受限制）
     */
    @Value("${app.security.duplicate-login.check-same-device:false}")
    private boolean checkSameDevice;
    
    @Override
    public DuplicateLoginResult checkAndHandleDuplicateLogin(Long memberId, HttpServletRequest request) {
        // 1. 获取当前活跃会话
        List<MemberSession> activeSessions = memberSessionService.getActiveSessionsForMember(memberId);
        log.info("用户 {} 重复登录检查：当前活跃会话数量 {}", memberId, activeSessions.size());

        // 调试信息：打印会话详情
        if (log.isDebugEnabled()) {
            for (MemberSession session : activeSessions) {
                log.debug("活跃会话 - ID: {}, IP: {}, 登录时间: {}, Token前缀: {}",
                        session.getId(), session.getIpAddress(), session.getLoginTime(),
                        session.getToken() != null ? session.getToken().substring(0, Math.min(8, session.getToken().length())) : "null");
            }
        }

        // 2. 如果没有活跃会话，直接允许登录
        if (activeSessions.isEmpty()) {
            log.info("用户 {} 首次登录，允许", memberId);
            return new DuplicateLoginResult(true, "首次登录，允许");
        }

        // 3. 检查是否为同设备登录
        String currentIp = IpUtils.getIpAddr(request);
        String currentUserAgent = request.getHeader("User-Agent");
        log.info("用户 {} 当前登录IP: {}, UserAgent: {}", memberId, currentIp, currentUserAgent);

        int originalSessionCount = activeSessions.size();
        if (!checkSameDevice) {
            // 过滤掉同设备的会话（同设备重复登录不受限制）
            activeSessions = activeSessions.stream()
                    .filter(session -> !isSameDevice(session, currentIp, currentUserAgent))
                    .toList();
            log.info("用户 {} 过滤同设备会话后：原始会话数 {}, 过滤后会话数 {}",
                    memberId, originalSessionCount, activeSessions.size());
        }

        // 4. 检查会话数量是否超限
        if (activeSessions.size() < maxConcurrentSessions) {
            log.info("用户 {} 会话数量未超限：当前 {} < 最大 {}, 允许登录",
                    memberId, activeSessions.size(), maxConcurrentSessions);
            return new DuplicateLoginResult(true, "会话数量未超限，允许登录");
        }

        // 5. 根据策略处理重复登录
        log.info("用户 {} 会话数量超限：当前 {} >= 最大 {}, 执行策略 {}",
                memberId, activeSessions.size(), maxConcurrentSessions, duplicateLoginStrategy);
        return handleDuplicateLoginByStrategy(memberId, activeSessions);
    }
    
    @Override
    public int getActiveSessionCount(Long memberId) {
        List<MemberSession> activeSessions = memberSessionService.getActiveSessionsForMember(memberId);
        return activeSessions.size();
    }
    
    @Override
    @Transactional
    public void kickOldestSessions(Long memberId, int keepCount) {
        List<MemberSession> activeSessions = memberSessionService.getActiveSessionsForMember(memberId);
        
        if (activeSessions.size() <= keepCount) {
            return; // 无需踢掉任何会话
        }
        
        // 按登录时间排序，踢掉最旧的会话
        activeSessions.sort(Comparator.comparing(MemberSession::getLoginTime));
        
        int sessionsToKick = activeSessions.size() - keepCount;
        for (int i = 0; i < sessionsToKick; i++) {
            MemberSession sessionToKick = activeSessions.get(i);
            memberSessionService.terminateSession(memberId, sessionToKick.getId());
            log.info("踢掉用户 {} 的旧会话，会话ID: {}, 登录时间: {}", 
                    memberId, sessionToKick.getId(), sessionToKick.getLoginTime());
        }
    }
    
    /**
     * 根据策略处理重复登录
     */
    private DuplicateLoginResult handleDuplicateLoginByStrategy(Long memberId, List<MemberSession> activeSessions) {
        switch (duplicateLoginStrategy) {
            case BaseConstants.DuplicateLoginStrategy.ALLOW_MULTIPLE:
                return new DuplicateLoginResult(true, "策略允许多设备登录");
                
            case BaseConstants.DuplicateLoginStrategy.KICK_OLD:
                // 踢掉最旧的会话，为新登录腾出空间
                List<MemberSession> kickedSessions = kickOldestSessionsAndReturn(memberId, maxConcurrentSessions - 1);
                return new DuplicateLoginResult(true, 
                        String.format("已踢掉 %d 个旧会话，允许新登录", kickedSessions.size()), 
                        kickedSessions);
                
            case BaseConstants.DuplicateLoginStrategy.REJECT_NEW:
                throw new BusinessException(CodeConstants.USER_ACCOUNT_LOCKED, 
                        String.format("您的账号已在其他设备登录，最多允许 %d 个设备同时在线", maxConcurrentSessions));
                
            default:
                log.warn("未知的重复登录策略: {}, 使用默认策略 KICK_OLD", duplicateLoginStrategy);
                List<MemberSession> defaultKickedSessions = kickOldestSessionsAndReturn(memberId, maxConcurrentSessions - 1);
                return new DuplicateLoginResult(true,
                        String.format("已踢掉 %d 个旧会话，允许新登录", defaultKickedSessions.size()),
                        defaultKickedSessions);
        }
    }

    /**
     * 踢掉最旧的会话并返回被踢掉的会话列表
     */
    @Transactional
    private List<MemberSession> kickOldestSessionsAndReturn(Long memberId, int keepCount) {
        List<MemberSession> activeSessions = memberSessionService.getActiveSessionsForMember(memberId);
        List<MemberSession> kickedSessions = new ArrayList<>();

        if (activeSessions.size() <= keepCount) {
            // 无需踢掉任何会话
            return kickedSessions;
        }

        // 按登录时间排序，踢掉最旧的会话
        activeSessions.sort(Comparator.comparing(MemberSession::getLoginTime));

        int sessionsToKick = activeSessions.size() - keepCount;
        log.info("用户 {} 需要踢掉 {} 个旧会话，保留 {} 个会话", memberId, sessionsToKick, keepCount);

        for (int i = 0; i < sessionsToKick; i++) {
            MemberSession sessionToKick = activeSessions.get(i);
            log.info("准备踢掉用户 {} 的旧会话，会话ID: {}, 登录时间: {}, Token前缀: {}",
                    memberId, sessionToKick.getId(), sessionToKick.getLoginTime(),
                    sessionToKick.getToken() != null ? sessionToKick.getToken().substring(0, Math.min(8, sessionToKick.getToken().length())) : "null");

            memberSessionService.terminateSession(memberId, sessionToKick.getId());
            kickedSessions.add(sessionToKick);

            // 将被踢掉的JWT Token加入黑名单
            addTokenToBlacklist(sessionToKick.getToken());

            log.info("成功踢掉用户 {} 的旧会话，会话ID: {}, 登录时间: {}",
                    memberId, sessionToKick.getId(), sessionToKick.getLoginTime());
        }

        // 踢掉会话后不需要在这里刷新缓存，因为调用方会处理
        return kickedSessions;
    }

    /**
     * 将Token加入黑名单
     */
    private void addTokenToBlacklist(String token) {
        if (token != null && !token.isEmpty()) {
            try {
                // 设置较长的过期时间，确保Token在其自然过期前都在黑名单中
                String blacklistKey = CacheConstants.BFF_TOKEN_BLACKLIST_KEY + ":" + token;
                redisTemplate.opsForValue().set(blacklistKey, "kicked", 24, TimeUnit.HOURS);
                log.debug("已将Token加入黑名单: {}", token);
            } catch (Exception e) {
                log.error("将Token加入黑名单失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 判断是否为同一设备
     * 当前使用IP地址判断，后续可以扩展为更复杂的设备指纹识别
     */
    private boolean isSameDevice(MemberSession session, String currentIp, String currentUserAgent) {
        // 检查IP地址
        boolean sameIp = session.getIpAddress() != null && session.getIpAddress().equals(currentIp);

        log.debug("设备判断 - 会话ID: {}, 会话IP: {}, 当前IP: {}, IP相同: {}",
                session.getId(), session.getIpAddress(), currentIp, sameIp);

        // 目前只使用IP地址判断，后续可以结合deviceInfo等字段进行更精确的判断
        return sameIp;
    }
}
