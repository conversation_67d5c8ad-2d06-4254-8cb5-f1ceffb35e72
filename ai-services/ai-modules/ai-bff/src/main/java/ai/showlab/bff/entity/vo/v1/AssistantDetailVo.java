package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

/**
 * 助手详情视图对象
 * 用于向前端展示助手详细信息
 * 
 * <AUTHOR>
 */
@Data
public class AssistantDetailVo {
    
    /**
     * 助手ID
     */
    private Long id;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 所有者会员ID
     */
    private Long ownerMemberId;
    
    /**
     * 所有者昵称
     */
    private String ownerNickname;
    
    /**
     * 所有者收入分成比例
     */
    private BigDecimal revenueShareRate;
    
    /**
     * 助手编码
     */
    private String code;
    
    /**
     * 助手名称
     */
    private String name;
    
    /**
     * 助手描述
     */
    private String description;
    
    /**
     * 助手图标URL
     */
    private String iconUrl;
    
    /**
     * 提示词模板
     */
    private String promptTemplate;
    
    /**
     * 交互模式 (字典: assistant_interaction_mode)
     */
    private Integer interactionMode;

    /**
     * 交互模式名称（通过数据字典获取）
     */
    private String interactionModeName;

    /**
     * 模型建议配置 (JSON格式)
     */
    private String modelSuggestions;

    /**
     * 推荐模型列表
     */
    private List<ModelListVo> recommendedModels;

    /**
     * 版本号
     */
    private String version;

    /**
     * 助手状态 (字典: assistant_status)
     */
    private Integer status;

    /**
     * 助手状态名称（通过数据字典获取）
     */
    private String statusName;
    
    /**
     * 使用次数
     */
    private Long usageCount;
    
    /**
     * 是否公开
     */
    private Boolean isPublic;
    
    /**
     * 是否为预设助手
     */
    private Boolean isPreset;
    
    /**
     * 付费套餐IDs
     */
    private String billingPackageIds;
    
    /**
     * 知识库IDs
     */
    private String knowledgeIds;
    
    /**
     * 关联的知识库列表
     */
    private List<KnowledgeBaseVo> knowledgeBases;
    
    /**
     * 助手参数配置列表
     */
    private List<AssistantParamVo> params;
    
    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
    
    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
    
    /**
     * 是否已被当前会员添加
     */
    private Boolean isAdded;
    
    /**
     * 是否被当前会员收藏
     */
    private Boolean isFavorited;
    
    /**
     * 当前会员的助手实例ID（如果已添加）
     */
    private Long memberAssistantId;
}
