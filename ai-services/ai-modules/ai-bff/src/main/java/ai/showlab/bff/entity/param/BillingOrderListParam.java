package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 订单列表查询参数
 * <p>
 * 用于查询当前会员的订单历史，支持按状态、时间范围等条件筛选。
 * </p>
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class BillingOrderListParam {
    
    /**
     * 订单状态，可选 (字典: order_status), 1-待支付, 2-已完成, 3-已取消, 4-支付失败
     */
    private Integer status;
    
    /**
     * 开始时间，可选
     */
    private OffsetDateTime startTime;
    
    /**
     * 结束时间，可选
     */
    private OffsetDateTime endTime;
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
