package ai.showlab.bff.service.common.impl;

import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.common.IDuplicateSubmissionTokenService;
import ai.showlab.common.core.constant.CacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交 Token 服务实现类
 * 使用 Redis 存储 Token，确保其唯一性和一次性使用。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DuplicateSubmissionTokenServiceImpl extends BaseService implements IDuplicateSubmissionTokenService {
    // Token 有效期，单位秒
    private static final long EXPIRE_TIME = 30;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public String generateToken(String userId) {
        String token = UUID.randomUUID().toString().replace("-", "");
        String key = CacheConstants.DUPLICATE_SUBMISSION_PREFIX + userId + ":" + token;
        redisTemplate.opsForValue().set(key, "1", EXPIRE_TIME, TimeUnit.SECONDS);
        log.info("为用户 {} 生成防重复提交 Token: {}", userId, token);
        return token;
    }

    @Override
    public boolean validateAndConsumeToken(String token, String userId) {
        if (!StringUtils.hasText(token)) {
            throw new BusinessException("防重复提交Token不能为空");
        }

        String key = CacheConstants.DUPLICATE_SUBMISSION_PREFIX + userId + ":" + token;
        // 尝试删除 Token，如果删除成功（即 Token 存在且未被消耗），则验证通过
        if (redisTemplate.delete(key)) {
            log.info("成功验证并消耗防重复提交 Token: {} for user {}", token, userId);
            return true;
        }
        log.warn("防重复提交 Token 无效或已被消耗。Token: {}, UserId: {}", token, userId);
        throw new BusinessException("请勿重复提交，请刷新页面重试");
    }
} 