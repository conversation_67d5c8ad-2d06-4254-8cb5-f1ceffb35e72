package ai.showlab.bff.service.common.impl;

import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.common.INotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 通知服务实现类
 * 目前为模拟实现，实际需要集成第三方短信/邮件发送SDK。
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationServiceImpl extends BaseService implements INotificationService {

    @Override
    public void sendSmsCode(String phone, String code) {
        log.info("模拟发送短信验证码: {}. 手机号: {}", code, phone);
        // TODO: 集成实际的短信发送SDK，例如阿里云短信服务、腾讯云短信服务等
    }

    @Override
    public void sendEmailCode(String email, String code) {
        log.info("模拟发送邮件验证码: {}. 邮箱: {}", code, email);
        // TODO: 集成实际的邮件发送SDK，例如 Spring Mail、JavaMail 等
    }
} 