package ai.showlab.bff.service.v1.log.impl;

import ai.showlab.bff.entity.domain.v1.log.LogModelRequest;
import ai.showlab.bff.entity.domain.v1.log.LogModelResponse;
import ai.showlab.bff.entity.dto.v1.LogRequestDto;
import ai.showlab.bff.entity.dto.v1.LogResponseDto;
import ai.showlab.bff.entity.vo.v1.ModelCallLogVo;
import ai.showlab.bff.mapper.v1.log.LogModelRequestMapper;
import ai.showlab.bff.mapper.v1.log.LogModelResponseMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.log.ILoggingService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 日志服务
 *
 * <AUTHOR>
 */
@Service
public class LoggingServiceImpl extends BaseService implements ILoggingService {

    @Autowired
    private LogModelRequestMapper requestMapper;

    @Autowired
    private LogModelResponseMapper responseMapper;

    @Override
    @Async("taskExecutor") // 假设已配置名为taskExecutor的线程池
    public void logRequest(LogRequestDto requestDto) {
        LogModelRequest requestLog = new LogModelRequest();
        BeanUtils.copyProperties(requestDto, requestLog);
        requestMapper.insertLog(requestLog);
    }

    @Override
    @Async("taskExecutor")
    public void logResponse(LogResponseDto responseDto) {
        LogModelResponse responseLog = new LogModelResponse();
        BeanUtils.copyProperties(responseDto, responseLog);
        responseMapper.insertLog(responseLog);
    }

    @Override
    public PageInfo<ModelCallLogVo> getMemberCallHistory(Long memberId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<LogModelRequest> requestLogs = requestMapper.selectRequestLogsByMemberId(memberId);
        PageInfo<LogModelRequest> pageInfo = new PageInfo<>(requestLogs);

        // 转换为VO。注意：这里为了简化，没有去关联查询response。实际场景可能需要。
        List<ModelCallLogVo> voList = requestLogs.stream().map(r -> {
            ModelCallLogVo vo = new ModelCallLogVo();
            vo.setRequestId(r.getRequestId());
            vo.setModelId(r.getModelId());
            vo.setInputSummary(r.getInputSummary());
            vo.setStatus(r.getStatus());
            vo.setRequestTime(r.getRequestTime());
            // vo.setStatusDesc(...) // 转换状态码为文字
            return vo;
        }).collect(Collectors.toList());

        PageInfo<ModelCallLogVo> voPageInfo = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        return voPageInfo;
    }
} 