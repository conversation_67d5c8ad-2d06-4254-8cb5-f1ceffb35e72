package ai.showlab.bff.config;

import ai.showlab.bff.common.constant.ApiUrlConstants;
import ai.showlab.bff.common.interceptor.ApiAuthInterceptor;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 * <p>
 * 统一配置所有的Web MVC相关组件，包括拦截器等。
 * 合并了原有的ApiAuthConfig和WebConfig的功能。
 * </p>
 * 
 * <h3>配置的拦截器：</h3>
 * <ul>
 *   <li>TraceIdInterceptor：链路追踪拦截器（拦截所有路径，优先级0）</li>
 *   <li>ApiAuthInterceptor：API权限认证拦截器（拦截API路径，优先级100）</li>
 * </ul>
 * 
 * <h3>拦截策略：</h3>
 * <ul>
 *   <li>TraceId拦截器：拦截所有/**路径，用于链路追踪</li>
 *   <li>权限拦截器：拦截/api/**路径，排除公共路径，根据@ApiAuth注解进行权限校验</li>
 * </ul>
 * 
 * <h3>执行顺序：</h3>
 * <ol>
 *   <li>TraceIdInterceptor (order=0) - 最先执行，设置链路追踪ID</li>
 *   <li>ApiAuthInterceptor (order=100) - 后执行，进行权限校验</li>
 * </ol>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private TraceIdInterceptor traceIdInterceptor;

    @Autowired
    private ApiAuthInterceptor apiAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 1. 注册TraceId拦截器（链路追踪）
        // 拦截所有路径，优先级最高（order=0，默认值）
        // 用于为每个请求生成唯一的追踪ID，便于日志追踪和问题排查
        registry.addInterceptor(traceIdInterceptor)
                .addPathPatterns("/**")
                .order(0);

        // 2. 注册API权限认证拦截器
        // 拦截API路径，排除公共路径，优先级较低（order=100）
        // 根据@ApiAuth注解进行权限校验，支持匿名访问、登录访问、权限访问三种模式
        registry.addInterceptor(apiAuthInterceptor)
                .addPathPatterns(ApiUrlConstants.API_PATTERN)
                .excludePathPatterns(ApiUrlConstants.PUBLIC_PATHS)
                .order(100);
    }
}
