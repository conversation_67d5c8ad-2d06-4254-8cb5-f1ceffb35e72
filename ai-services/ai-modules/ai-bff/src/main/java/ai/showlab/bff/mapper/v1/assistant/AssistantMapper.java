package ai.showlab.bff.mapper.v1.assistant;

import ai.showlab.bff.entity.domain.v1.assistant.Assistant;
import ai.showlab.bff.entity.param.AssistantListParam;
import ai.showlab.bff.entity.vo.v1.AssistantDetailVo;
import ai.showlab.bff.entity.vo.v1.AssistantListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI助手 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface AssistantMapper {

    /**
     * 查询公共AI助手列表
     *
     * @return AI助手列表
     */
    List<Assistant> selectPublicAssistants();

    /**
     * 根据会员ID查询其创建的AI助手列表
     *
     * @param memberId 会员ID
     * @return AI助手列表
     */
    List<Assistant> selectAssistantsByMemberId(Long memberId);

    /**
     * 根据ID查询AI助手详情
     *
     * @param id AI助手ID
     * @return AI助手信息
     */
    Assistant selectAssistantById(Long id);

    /**
     * 增加AI助手使用次数
     *
     * @param id AI助手ID
     * @return 影响行数
     */
    int incrementUsageCount(Long id);

    // ==================== 新增方法（面向外部API） ====================

    /**
     * 分页查询助手列表（返回VO对象）
     *
     * @param param 查询参数
     * @return 助手列表VO
     */
    List<AssistantListVo> selectAssistantListVoByCondition(AssistantListParam param);

    /**
     * 统计符合条件的助手数量
     *
     * @param param 查询参数
     * @return 总数量
     */
    long countAssistantsByCondition(AssistantListParam param);

    /**
     * 根据ID查询助手详情（返回VO对象）
     *
     * @param id 助手ID
     * @return 助手详情VO
     */
    AssistantDetailVo selectAssistantDetailVoById(Long id);

    /**
     * 根据编码查询助手详情（返回VO对象）
     *
     * @param code 助手编码
     * @return 助手详情VO
     */
    AssistantDetailVo selectAssistantDetailVoByCode(String code);

    /**
     * 查询热门助手列表（返回VO对象）
     *
     * @param limit 限制数量
     * @return 热门助手列表VO
     */
    List<AssistantListVo> selectPopularAssistantsVo(int limit);

    /**
     * 统计指定分类下的助手数量
     *
     * @param categoryId 分类ID
     * @return 助手数量
     */
    Long countAssistantsByCategory(Long categoryId);

    /**
     * 根据编码查询助手信息
     *
     * @param code 助手编码
     * @return 助手信息
     */
    Assistant selectAssistantByCode(String code);

    /**
     * 查询会员可访问的助手列表（返回VO对象）
     *
     * @param param 查询参数
     * @param memberId 会员ID
     * @return 助手列表VO
     */
    List<AssistantListVo> selectAccessibleAssistantsVo(@Param("param") AssistantListParam param, @Param("memberId") Long memberId);

    /**
     * 统计会员可访问的助手数量
     *
     * @param param 查询参数
     * @param memberId 会员ID
     * @return 总数量
     */
    long countAccessibleAssistants(@Param("param") AssistantListParam param, @Param("memberId") Long memberId);

    /**
     * 查询会员收藏的助手列表（返回VO对象）
     *
     * @param memberId 会员ID
     * @return 收藏的助手列表VO
     */
    List<AssistantListVo> selectFavoriteAssistantsVo(Long memberId);

}