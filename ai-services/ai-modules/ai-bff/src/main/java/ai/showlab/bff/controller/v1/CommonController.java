package ai.showlab.bff.controller.v1;


import ai.showlab.bff.common.annotation.ApiAuth;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.domain.v1.base.BaseCountry;
import ai.showlab.bff.entity.domain.v1.base.BaseCurrency;
import ai.showlab.bff.entity.domain.v1.base.BaseDocument;
import ai.showlab.bff.entity.domain.v1.base.BaseLang;
import ai.showlab.bff.service.v1.base.IBaseCountryService;
import ai.showlab.bff.service.v1.base.IBaseCurrencyService;
import ai.showlab.bff.service.v1.base.IBaseDocumentService;
import ai.showlab.bff.service.v1.base.IBaseLangService;
import ai.showlab.common.core.web.domain.RestResult;
import ai.showlab.common.protocol.enums.ApiAuthTypeEnum;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 通用请求
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/common/")
public class CommonController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private IBaseLangService langService;

    @Autowired
    private IBaseCountryService countryService;

    @Autowired
    private IBaseCurrencyService currencyService;

    @Autowired
    private IBaseDocumentService documentService;

    /**
     * 根据编码和语言获取已发布的文档
     *
     * @param code 文档编码, 例如: "privacy-policy", "terms-of-service"
     * @param lang 语言ID
     * @return 文档内容
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/document")
    public ResponseEntity<RestResult> getDocument(
            @RequestParam("code") @NotBlank(message = "文档编码不能为空") String code,
            @RequestParam("lang") @NotNull(message = "语言ID不能为空") Long lang) {
        return executeWithTryCatch(() -> {
            BaseDocument document = documentService.getPublishedDocumentByCode(code, lang);
            return checkNotNull(document, "文档不存在");
        }, "获取文档失败，请稍后重试");
    }

    /**
     * 获取所有可用货币列表
     *
     * @return 货币列表
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/currency/list")
    public ResponseEntity<RestResult> listCurrency() {
        return executeWithTryCatch(() -> currencyService.getAllCurrencies(), "获取货币列表失败，请稍后重试");
    }

    /**
     * 根据ID获取货币信息
     *
     * @param id 货币ID
     * @return 货币信息
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/currency/{id}")
    public ResponseEntity<RestResult> getCurrencyById(@PathVariable("id") @Min(value = 1, message = "ID 必须大于 0") Long id) {
        return executeWithTryCatch(() -> {
            BaseCurrency data = currencyService.getCurrencyById(id);
            return checkNotNull(data, "货币不存在");
        }, "获取货币信息失败，请稍后重试");
    }

    /**
     * 获取默认货币
     *
     * @return 默认货币信息
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/currency/default")
    public ResponseEntity<RestResult> getDefaultCurrency() {
        return executeWithTryCatch(() -> {
            BaseCurrency data = currencyService.getDefaultCurrency();
            return checkNotNull(data, "默认货币未配置");
        }, "获取默认货币失败，请稍后重试");
    }

    /**
     * 获取所有可用国家列表
     *
     * @return 国家列表
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/country/list")
    public ResponseEntity<RestResult> listCountry() {
        return executeWithTryCatch(() -> countryService.getAllCountries(), "获取国家列表失败，请稍后重试");
    }

    /**0
     * 根据ID获取国家信息
     *
     * @param id 国家ID
     * @return 国家信息
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/country/{id}")
    public ResponseEntity<RestResult> getCountryById(@PathVariable("id") @Min(value = 1, message = "ID 必须大于 0") Long id) {
        return executeWithTryCatch(() -> {
            BaseCountry vo = countryService.getCountryById(id);
            return checkNotNull(vo, "国家不存在");
        }, "获取国家信息失败，请稍后重试");
    }

    /**
     * 获取所有可用语言列表
     *
     * @return 语言列表
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/lang/list")
    public ResponseEntity<RestResult> listLang() {
        return executeWithTryCatch(() -> langService.getAllLangs(), "获取语言列表失败，请稍后重试");
    }

    /**
     * 根据ID获取语言信息
     *
     * @param id 语言ID
     * @return 语言信息
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/lang/{id}")
    public ResponseEntity<RestResult> getLangById(@PathVariable("id") @Min(value = 1, message = "ID 必须大于 0") Long id) {
        return executeWithTryCatch(() -> {
            BaseLang lang = langService.getLangById(id);
            return checkNotNull(lang, "语言不存在");
        }, "获取语言信息失败，请稍后重试");
    }

    /**
     * 获取默认语言
     *
     * @return 默认语言信息
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @GetMapping("/lang/default")
    public ResponseEntity<RestResult> getDefaultLang() {
        return executeWithTryCatch(() -> {
            BaseLang lang = langService.getDefaultLang();
            return checkNotNull(lang, "默认语言未配置");
        }, "获取默认语言失败，请稍后重试");
    }

}
