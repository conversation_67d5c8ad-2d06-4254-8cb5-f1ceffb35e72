package ai.showlab.bff.common.aop;

import ai.showlab.bff.common.annotation.CustomCache;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 自定义缓存注解切面，实现带TTL的缓存功能
 *
 * <AUTHOR>
 * @date 2024-08-01
 */
@Aspect
@Component
@DependsOn("redisTemplate")
public class CustomCacheAspect {
    private static final Logger log = LoggerFactory.getLogger(CustomCacheAspect.class);

    @Autowired
    public RedisTemplate<String, Object> redisTemplate;

    private final SpelExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    // 缓存已解析的SpEL表达式，避免重复解析
    private final ConcurrentHashMap<Method, Expression> keyExpressionsCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Method, Expression> unlessExpressionsCache = new ConcurrentHashMap<>();

    @Around("@annotation(customCache)")
    public Object around(ProceedingJoinPoint joinPoint, CustomCache customCache) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取缓存名称和键，cacheName 仍然可以用于区分不同的缓存业务，但TTL由注解控制
        String cacheName = customCache.value(); 
        String keyExpression = customCache.key();
        long ttl = customCache.ttl();
        
        // 解析SpEL表达式获取缓存键
        String cacheKey = generateKey(keyExpression, method, joinPoint.getArgs());
        
        // 拼接完整的缓存键： cacheName::cacheKey
        String fullCacheKey = cacheName + "::" + cacheKey;

        // 尝试从Redis获取
        Object value = redisTemplate.opsForValue().get(fullCacheKey);
        if (value != null) {
            log.debug("Cache hit for key: {}", fullCacheKey);
            // 处理FastJson2反序列化的类型转换问题
            return convertCachedValue(value, method);
        }
        
        // 缓存未命中，执行方法
        log.debug("Cache miss for key: {}", fullCacheKey);
        Object result = joinPoint.proceed();

        EvaluationContext evaluationContext = createEvaluationContext(method, joinPoint.getArgs(), result);

        // 检查unless条件
        String unlessExpression = customCache.unless();
        if (!unlessExpression.isEmpty()) {
            try {
                // 从缓存获取或解析unless表达式
                Expression parsedUnlessExpression = unlessExpressionsCache.computeIfAbsent(method, k -> parser.parseExpression(unlessExpression));
                if (Boolean.TRUE.equals(parsedUnlessExpression.getValue(evaluationContext, Boolean.class))) {
                    log.debug("Unless condition met for key: {}, result will not be cached", fullCacheKey);
                    return result;
                }
            } catch (Exception e) {
                log.warn("Failed to evaluate unless expression '{}' for key {}: {}", unlessExpression, fullCacheKey, e.getMessage());
                // 如果unless表达式评估失败，默认不缓存
                return result;
            }
        }

        // 缓存结果到Redis并设置过期时间
        if (ttl > 0) {
            redisTemplate.opsForValue().set(fullCacheKey, result, ttl, TimeUnit.SECONDS);
            log.debug("Cache put for key: {} with TTL {} seconds", fullCacheKey, ttl);
        } else {
            redisTemplate.opsForValue().set(fullCacheKey, result);
            log.debug("Cache put for key: {} (no TTL)", fullCacheKey);
        }

        return result;
    }

    /**
     * 转换缓存值，处理FastJson2反序列化的类型问题
     */
    private Object convertCachedValue(Object cachedValue, Method method) {
        if (cachedValue == null) {
            return null;
        }

        // 获取方法返回类型
        Type returnType = method.getGenericReturnType();
        Class<?> returnClass = method.getReturnType();

        try {
            // 如果缓存值是JSONObject，需要转换为目标类型
            if (cachedValue instanceof JSONObject jsonObject) {
                return jsonObject.toJavaObject(returnClass);
            }

            // 如果缓存值是List且包含JSONObject，需要转换List中的元素
            if (cachedValue instanceof List<?> list && returnType instanceof ParameterizedType parameterizedType) {
                if (!list.isEmpty() && list.getFirst() instanceof JSONObject) {
                    Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                    if (actualTypeArguments.length > 0 && actualTypeArguments[0] instanceof Class<?> elementClass) {
                        return JSON.parseArray(JSON.toJSONString(list), elementClass);
                    }
                }
            }

            // 如果类型匹配或者是基本类型，直接返回
            if (returnClass.isInstance(cachedValue) || returnClass.isPrimitive() ||
                returnClass == String.class || Number.class.isAssignableFrom(returnClass) ||
                returnClass == Boolean.class) {
                return cachedValue;
            }

            // 其他情况，尝试通过JSON转换
            return JSON.parseObject(JSON.toJSONString(cachedValue), returnClass);

        } catch (Exception e) {
            log.warn("Failed to convert cached value for method {}: {}, returning original value",
                    method.getName(), e.getMessage());
            return cachedValue;
        }
    }

    private String generateKey(String keyExpression, Method method, Object[] args) {
        if (keyExpression.isEmpty()) {
            // 如果没有指定键表达式，使用方法名和参数生成
            return createDefaultKey(method, args);
        }
        // 解析SpEL表达式
        EvaluationContext context = createEvaluationContext(method, args, null);
        try {
            // 从缓存获取或解析key表达式
            Expression parsedKeyExpression = keyExpressionsCache.computeIfAbsent(method, k -> parser.parseExpression(keyExpression));
            Object value = parsedKeyExpression.getValue(context);
            return value != null ? value.toString() : "null";
        } catch (Exception e) {
            log.warn("Failed to evaluate key expression '{}' for method {}: {}", keyExpression, method.getName(), e.getMessage());
            // 如果表达式评估失败，使用默认键
            return createDefaultKey(method, args);
        }
    }

    /**
     * 根据方法名和参数生成默认缓存键
     */
    private String createDefaultKey(Method method, Object[] args) {
        StringBuilder key = new StringBuilder(method.getName());
        for (Object arg : args) {
            key.append("_").append(arg != null ? arg.toString() : "null");
        }
        return key.toString();
    }
    
    private EvaluationContext createEvaluationContext(Method method, Object[] args, Object result) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        // 添加方法参数
        String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        // 添加方法返回值
        if (result != null) {
            context.setVariable("result", result);
        }
        return context;
    }
} 