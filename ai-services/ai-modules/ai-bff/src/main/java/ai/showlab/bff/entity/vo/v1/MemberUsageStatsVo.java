package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;

/**
 * 会员使用统计VO
 * 
 * <AUTHOR>
 */
@Data
public class MemberUsageStatsVo {
    
    /**
     * 会员ID
     */
    private Long memberId;
    
    /**
     * 总调用次数
     */
    private Long totalCalls;
    
    /**
     * 成功调用次数
     */
    private Long successCalls;
    
    /**
     * 失败调用次数
     */
    private Long failedCalls;
    
    /**
     * 超时调用次数
     */
    private Long timeoutCalls;
    
    /**
     * 成功率（百分比）
     */
    private BigDecimal successRate;
    
    /**
     * 使用的模型数量
     */
    private Long usedModels;
    
    /**
     * 总输入Token数
     */
    private Long totalInputTokens;
    
    /**
     * 总输出Token数
     */
    private Long totalOutputTokens;
    
    /**
     * 总Token数
     */
    private Long totalTokens;
    
    /**
     * 平均响应时间（毫秒）
     */
    private BigDecimal avgResponseTime;
    
    /**
     * 首次调用日期
     */
    private LocalDate firstCallDate;
    
    /**
     * 最后调用日期
     */
    private OffsetDateTime lastCallDate;
    
    /**
     * 统计时间
     */
    private OffsetDateTime statsTime;

    /**
     * 助手总数
     */
    private Long totalAssistants;

    /**
     * 激活的助手数量
     */
    private Long activeAssistants;

    /**
     * 收藏的助手数量
     */
    private Long favoriteAssistants;

    /**
     * 总使用次数
     */
    private Long totalUsageCount;

    /**
     * 今日使用次数
     */
    private Long todayUsageCount;

    /**
     * 本周使用次数
     */
    private Long weekUsageCount;

    /**
     * 本月使用次数
     */
    private Long monthUsageCount;
}
