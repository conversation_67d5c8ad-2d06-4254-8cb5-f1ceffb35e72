package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiAuth;
import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.docs.AssistantApiAnnotations;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.ParamValidateUtil;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.bff.service.v1.assistant.IAssistantCategoryService;
import ai.showlab.bff.service.v1.assistant.IAssistantService;
import ai.showlab.bff.service.v1.member.IMemberAssistantService;
import ai.showlab.common.core.web.domain.RestResult;
import ai.showlab.common.core.web.page.PageResult;
import ai.showlab.common.protocol.enums.ApiAuthTypeEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AI助手接口 (面向外部普通会员)
 * 提供AI助手相关的查询、管理功能，包括助手列表、详情、分类等
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/assistant")
@Tag(name = "AI助手接口", description = "提供AI助手的查询、管理功能，包括助手列表、详情、分类、会员助手实例管理等，面向普通会员使用。")
public class AssistantController extends BaseController {

    @Autowired
    private IAssistantService assistantService;
    
    @Autowired
    private IAssistantCategoryService assistantCategoryService;
    
    @Autowired
    private IMemberAssistantService memberAssistantService;

    // ==================== 助手分类相关接口 ====================

    /**
     * 获取助手分类列表
     * @return 分类树形列表
     */
    @AssistantApiAnnotations.GetAssistantCategoriesApiDoc()
    @GetMapping("/categories")
    public ResponseEntity<RestResult> getAssistantCategories() {
        return executeWithTryCatch(() -> {
            List<AssistantCategoryVo> categories = assistantCategoryService.getAllCategoriesAsTree();
            return RestResult.ok("获取成功", categories);
        }, "获取助手分类失败，请稍后重试");
    }

    // ==================== 助手查询相关接口 ====================

    /**
     * 获取助手列表
     * @param requestParams 查询参数
     * @return 助手列表
     */
    @AssistantApiAnnotations.GetAssistantListApiDoc()
    @ApiParamValidate(bizParamClass = AssistantListParam.class)
    @GetMapping("/list")
    public ResponseEntity<RestResult> getAssistantList(RequestParams<AssistantListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            PageResult<AssistantListVo> result = assistantService.getAssistantList(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取助手列表失败，请稍后重试");
    }

    /**
     * 获取助手详情
     * @param requestParams 查询参数
     * @return 助手详情
     */
    @AssistantApiAnnotations.GetAssistantDetailApiDoc()
    @ApiParamValidate(bizParamClass = AssistantDetailParam.class)
    @GetMapping("/detail")
    public ResponseEntity<RestResult> getAssistantDetail(RequestParams<AssistantDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            AssistantDetailVo result = assistantService.getAssistantDetail(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取助手详情失败，请稍后重试");
    }

    /**
     * 根据编码获取助手信息
     * @param requestParams 查询参数
     * @return 助手信息
     */
    @AssistantApiAnnotations.GetAssistantByCodeApiDoc()
    @ApiParamValidate(bizParamClass = AssistantByCodeParam.class)
    @GetMapping("/by-code")
    public ResponseEntity<RestResult> getAssistantByCode(RequestParams<AssistantByCodeParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            AssistantDetailVo result = assistantService.getAssistantByCode(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取助手信息失败，请稍后重试");
    }

    /**
     * 获取热门助手列表
     * @return 热门助手列表
     */
    @AssistantApiAnnotations.GetPopularAssistantsApiDoc()
    @GetMapping("/popular")
    public ResponseEntity<RestResult> getPopularAssistants() {
        return executeWithTryCatch(() -> {
            List<AssistantListVo> result = assistantService.getPopularAssistants();
            return RestResult.ok("获取成功", result);
        }, "获取热门助手失败，请稍后重试");
    }

    /**
     * 获取当前会员可访问的助手列表
     * @param requestParams 查询参数
     * @return 可访问的助手列表
     */
    @AssistantApiAnnotations.GetAccessibleAssistantsApiDoc()
    @ApiParamValidate(bizParamClass = AssistantListParam.class)
    @GetMapping("/accessible")
    public ResponseEntity<RestResult> getAccessibleAssistants(RequestParams<AssistantListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            PageResult<AssistantListVo> result = assistantService.getAccessibleAssistants(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取可访问助手列表失败，请稍后重试");
    }

    // ==================== 助手收藏相关接口 ====================

    /**
     * 获取当前会员收藏的助手列表
     * @return 收藏的助手列表
     */
    @AssistantApiAnnotations.GetFavoriteAssistantsApiDoc()
    @GetMapping("/favorites")
    public ResponseEntity<RestResult> getFavoriteAssistants() {
        return executeWithTryCatch(() -> {
            List<AssistantListVo> result = assistantService.getFavoriteAssistants();
            return RestResult.ok("获取成功", result);
        }, "获取收藏助手失败，请稍后重试");
    }

    /**
     * 收藏助手
     * @param requestParams 请求参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.FavoriteAssistantApiDoc()
    @ApiParamValidate(bizParamClass = AssistantDetailParam.class)
    @PostMapping("/favorite")
    public ResponseEntity<RestResult> favoriteAssistant(RequestParams<AssistantDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            assistantService.favoriteAssistant(requestParams);
            return RestResult.ok("收藏成功");
        }, "收藏助手失败，请稍后重试");
    }

    /**
     * 取消收藏助手
     * @param requestParams 请求参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.UnfavoriteAssistantApiDoc()
    @ApiParamValidate(bizParamClass = AssistantDetailParam.class)
    @PostMapping("/unfavorite")
    public ResponseEntity<RestResult> unfavoriteAssistant(RequestParams<AssistantDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            assistantService.unfavoriteAssistant(requestParams);
            return RestResult.ok("取消收藏成功");
        }, "取消收藏助手失败，请稍后重试");
    }

    // ==================== 使用统计相关接口 ====================

    /**
     * 获取当前会员的助手使用统计
     * @return 使用统计信息
     */
    @AssistantApiAnnotations.GetMemberUsageStatsApiDoc()
    @GetMapping("/usage-stats")
    public ResponseEntity<RestResult> getMemberUsageStats() {
        return executeWithTryCatch(() -> {
            MemberUsageStatsVo result = assistantService.getMemberUsageStats();
            return RestResult.ok("获取成功", result);
        }, "获取使用统计失败，请稍后重试");
    }

    /**
     * 获取当前会员对指定助手的使用统计
     * @param requestParams 请求参数
     * @return 助手使用统计信息
     */
    @AssistantApiAnnotations.GetAssistantUsageStatsApiDoc()
    @ApiParamValidate(bizParamClass = AssistantDetailParam.class)
    @GetMapping("/usage-stats/detail")
    public ResponseEntity<RestResult> getAssistantUsageStats(RequestParams<AssistantDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            MemberAssistantUsageStatsVo result = assistantService.getAssistantUsageStats(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取助手使用统计失败，请稍后重试");
    }

    // ==================== 会员助手实例管理接口 ====================

    /**
     * 获取当前会员的助手实例列表
     * @param requestParams 查询参数
     * @return 会员助手实例列表
     */
    @AssistantApiAnnotations.GetMemberAssistantListApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantListParam.class)
    @GetMapping("/my-assistants")
    public ResponseEntity<RestResult> getMemberAssistantList(RequestParams<MemberAssistantListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            PageResult<MemberAssistantVo> result = memberAssistantService.getMemberAssistantList(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取我的助手列表失败，请稍后重试");
    }

    /**
     * 获取会员助手实例详情
     * @param requestParams 查询参数
     * @return 会员助手实例详情
     */
    @AssistantApiAnnotations.GetMemberAssistantDetailApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantDetailParam.class)
    @GetMapping("/my-assistants/detail")
    public ResponseEntity<RestResult> getMemberAssistantDetail(RequestParams<MemberAssistantDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            MemberAssistantDetailVo result = memberAssistantService.getMemberAssistantDetail(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取助手实例详情失败，请稍后重试");
    }

    /**
     * 创建会员助手实例
     * @param requestParams 创建参数
     * @return 新创建的助手实例信息
     */
    @ApiAuth(type = ApiAuthTypeEnum.PERMISSION_REQUIRED, code = "assistant:create", description = "创建助手实例需要权限")
    @AssistantApiAnnotations.CreateMemberAssistantApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantCreateParam.class)
    @PostMapping("/my-assistants")
    public ResponseEntity<RestResult> createMemberAssistant(RequestParams<MemberAssistantCreateParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            MemberAssistantVo result = memberAssistantService.createMemberAssistant(requestParams);
            return RestResult.ok("创建成功", result);
        }, "创建助手实例失败，请稍后重试");
    }

    /**
     * 更新会员助手实例配置
     * @param requestParams 更新参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.UpdateMemberAssistantApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantUpdateParam.class)
    @PostMapping("/my-assistants/update")
    public ResponseEntity<RestResult> updateMemberAssistant(RequestParams<MemberAssistantUpdateParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            memberAssistantService.updateMemberAssistant(requestParams);
            return RestResult.ok("更新成功");
        }, "更新助手实例失败，请稍后重试");
    }

    /**
     * 删除会员助手实例
     * @param requestParams 删除参数
     * @return 操作结果
     */
    @ApiAuth(type = ApiAuthTypeEnum.PERMISSION_REQUIRED, code = "assistant:delete", description = "删除助手实例需要权限")
    @AssistantApiAnnotations.DeleteMemberAssistantApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantDeleteParam.class)
    @PostMapping("/my-assistants/delete")
    public ResponseEntity<RestResult> deleteMemberAssistant(RequestParams<MemberAssistantDeleteParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            memberAssistantService.deleteMemberAssistant(requestParams);
            return RestResult.ok("删除成功");
        }, "删除助手实例失败，请稍后重试");
    }

    // ==================== 会员助手实例状态管理接口 ====================

    /**
     * 激活/停用会员助手实例
     * @param requestParams 切换参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.ToggleMemberAssistantStatusApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantToggleParam.class)
    @PostMapping("/my-assistants/toggle-status")
    public ResponseEntity<RestResult> toggleMemberAssistantStatus(RequestParams<MemberAssistantToggleParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            memberAssistantService.toggleMemberAssistantStatus(requestParams);
            return RestResult.ok("状态切换成功");
        }, "切换助手状态失败，请稍后重试");
    }

    /**
     * 收藏/取消收藏会员助手实例
     * @param requestParams 切换参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.ToggleMemberAssistantFavoriteApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantToggleParam.class)
    @PostMapping("/my-assistants/toggle-favorite")
    public ResponseEntity<RestResult> toggleMemberAssistantFavorite(RequestParams<MemberAssistantToggleParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            memberAssistantService.toggleMemberAssistantFavorite(requestParams);
            return RestResult.ok("收藏状态切换成功");
        }, "切换收藏状态失败，请稍后重试");
    }

    // ==================== 会员助手实例配置管理接口 ====================

    /**
     * 更新会员助手实例名称
     * @param requestParams 更新参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.UpdateMemberAssistantNameApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantUpdateNameParam.class)
    @PostMapping("/my-assistants/update-name")
    public ResponseEntity<RestResult> updateMemberAssistantName(RequestParams<MemberAssistantUpdateNameParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            memberAssistantService.updateMemberAssistantName(requestParams);
            return RestResult.ok("名称更新成功");
        }, "更新助手名称失败，请稍后重试");
    }

    /**
     * 更新会员助手实例模型
     * @param requestParams 更新参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.UpdateMemberAssistantModelApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantUpdateModelParam.class)
    @PostMapping("/my-assistants/update-model")
    public ResponseEntity<RestResult> updateMemberAssistantModel(RequestParams<MemberAssistantUpdateModelParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            memberAssistantService.updateMemberAssistantModel(requestParams);
            return RestResult.ok("模型更新成功");
        }, "更新助手模型失败，请稍后重试");
    }

    /**
     * 更新会员助手实例参数配置
     * @param requestParams 更新参数
     * @return 操作结果
     */
    @AssistantApiAnnotations.UpdateMemberAssistantSettingsApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantUpdateSettingsParam.class)
    @PostMapping("/my-assistants/update-settings")
    public ResponseEntity<RestResult> updateMemberAssistantSettings(RequestParams<MemberAssistantUpdateSettingsParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            memberAssistantService.updateMemberAssistantSettings(requestParams);
            return RestResult.ok("参数配置更新成功");
        }, "更新参数配置失败，请稍后重试");
    }

    // ==================== 会员助手实例快捷查询接口 ====================

    /**
     * 获取会员的激活助手实例列表
     * @return 激活的助手实例列表
     */
    @AssistantApiAnnotations.GetActiveMemberAssistantsApiDoc()
    @GetMapping("/my-assistants/active")
    public ResponseEntity<RestResult> getActiveMemberAssistants() {
        return executeWithTryCatch(() -> {
            List<MemberAssistantVo> result = memberAssistantService.getActiveMemberAssistants();
            return RestResult.ok("获取成功", result);
        }, "获取激活助手失败，请稍后重试");
    }

    /**
     * 获取会员的收藏助手实例列表
     * @return 收藏的助手实例列表
     */
    @AssistantApiAnnotations.GetFavoriteMemberAssistantsApiDoc()
    @GetMapping("/my-assistants/favorites")
    public ResponseEntity<RestResult> getFavoriteMemberAssistants() {
        return executeWithTryCatch(() -> {
            List<MemberAssistantVo> result = memberAssistantService.getFavoriteMemberAssistants();
            return RestResult.ok("获取成功", result);
        }, "获取收藏助手失败，请稍后重试");
    }

    /**
     * 获取会员助手实例的使用统计
     * @param requestParams 请求参数
     * @return 使用统计信息
     */
    @AssistantApiAnnotations.GetMemberAssistantUsageStatsApiDoc()
    @ApiParamValidate(bizParamClass = MemberAssistantDetailParam.class)
    @GetMapping("/my-assistants/usage-stats")
    public ResponseEntity<RestResult> getMemberAssistantUsageStats(RequestParams<MemberAssistantDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            MemberAssistantUsageStatsVo result = memberAssistantService.getMemberAssistantUsageStats(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取使用统计失败，请稍后重试");
    }
}
