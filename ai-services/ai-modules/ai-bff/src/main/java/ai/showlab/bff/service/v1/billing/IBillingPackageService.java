package ai.showlab.bff.service.v1.billing;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.param.BillingPackageDetailParam;
import ai.showlab.bff.entity.param.BillingPackageListParam;
import ai.showlab.bff.entity.vo.v1.BillingPackageVo;
import ai.showlab.common.core.web.page.PageResult;

import java.util.List;

/**
 * 计费套餐服务接口
 * <p>
 * 定义了面向外部用户的计费套餐相关业务逻辑。
 * 包括套餐查询、热门推荐等功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IBillingPackageService {

    // ==================== 套餐查询功能 ====================

    /**
     * 分页查询计费套餐列表
     * <p>
     * 支持按类型、价格范围、关键词等条件筛选，返回适合前端展示的VO对象。
     * 此方法会进行缓存以提高性能。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 分页结果
     */
    PageResult<BillingPackageVo> getPackageList(RequestParams<BillingPackageListParam> requestParams);

    /**
     * 根据ID获取套餐详情
     * <p>
     * 返回包含完整信息的套餐详情，包括价格、授予积分等。
     * 此方法会进行缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含套餐ID
     * @return 套餐详情VO
     */
    BillingPackageVo getPackageDetail(RequestParams<BillingPackageDetailParam> requestParams);

    /**
     * 获取热门套餐列表
     * <p>
     * 返回热门推荐的套餐列表，用于首页展示。
     * 此方法会进行缓存。
     * </p>
     *
     * @return 热门套餐列表
     */
    List<BillingPackageVo> getPopularPackages();

    /**
     * 获取推荐套餐列表
     * <p>
     * 根据当前会员的使用情况和等级推荐合适的套餐。
     * 此方法会进行缓存。
     * </p>
     *
     * @return 推荐套餐列表
     */
    List<BillingPackageVo> getRecommendedPackages();

    // ==================== 内部方法 ====================

    /**
     * 根据ID获取套餐信息（内部方法）
     * <p>
     * 用于内部业务逻辑，返回Domain对象。
     * </p>
     *
     * @param packageId 套餐ID
     * @return 套餐信息
     */
    ai.showlab.bff.entity.domain.v1.billing.BillingPackage getPackageById(Long packageId);

    /**
     * 检查套餐是否可用
     * <p>
     * 验证套餐状态、库存等。
     * </p>
     *
     * @param packageId 套餐ID
     * @return 是否可用
     */
    boolean isPackageAvailable(Long packageId);
}
