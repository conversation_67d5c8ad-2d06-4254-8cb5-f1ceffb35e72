package ai.showlab.bff.service.v1.assistant.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.assistant.AssistantCategory;
import ai.showlab.bff.entity.vo.v1.AssistantCategoryVo;
import ai.showlab.bff.mapper.v1.assistant.AssistantCategoryMapper;
import ai.showlab.bff.mapper.v1.assistant.AssistantMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.assistant.IAssistantCategoryService;
import ai.showlab.common.core.constant.CacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI助手分类服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Slf4j
@Service
public class AssistantCategoryServiceImpl extends BaseService implements IAssistantCategoryService {

    @Autowired
    private AssistantCategoryMapper categoryMapper;

    @Autowired
    private AssistantMapper assistantMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_ASSISTANT_CATEGORIES_KEY + "_vo", unless = "#result == null or #result.isEmpty()")
    public List<AssistantCategoryVo> getAllCategoriesAsTree() {
        try {
            List<AssistantCategory> allCategories = categoryMapper.selectAllCategories();
            List<AssistantCategory> categoryTree = buildCategoryTree(allCategories);
            return convertToVoTree(categoryTree);
        } catch (Exception e) {
            log.error("获取助手分类树失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_ASSISTANT_CATEGORIES_KEY, unless = "#result == null or #result.isEmpty()")
    public List<AssistantCategory> getAllCategoriesAsTreeDomain() {
        List<AssistantCategory> allCategories = categoryMapper.selectAllCategories();
        return buildCategoryTree(allCategories);
    }

    /**
     * 将Domain对象树转换为VO对象树
     *
     * @param categoryTree Domain对象树
     * @return VO对象树
     */
    private List<AssistantCategoryVo> convertToVoTree(List<AssistantCategory> categoryTree) {
        if (CollectionUtils.isEmpty(categoryTree)) {
            return new ArrayList<>();
        }
        return categoryTree.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());
    }

    /**
     * 将单个Domain对象转换为VO对象
     *
     * @param category Domain对象
     * @return VO对象
     */
    private AssistantCategoryVo convertToVo(AssistantCategory category) {
        AssistantCategoryVo vo = new AssistantCategoryVo();
        BeanUtils.copyProperties(category, vo);
        // 获取该分类下的助手数量
        try {
            Long assistantCount = assistantMapper.countAssistantsByCategory(category.getId());
            vo.setAssistantCount(assistantCount != null ? assistantCount : 0L);
        } catch (Exception e) {
            log.warn("获取分类{}下的助手数量失败", category.getId(), e);
            vo.setAssistantCount(0L);
        }
        // 递归转换子分类
        if (!CollectionUtils.isEmpty(category.getChildren())) {
            List<AssistantCategoryVo> childrenVo = category.getChildren().stream()
                    .map(this::convertToVo)
                    .collect(Collectors.toList());
            vo.setChildren(childrenVo);
        }
        return vo;
    }

    /**
     * 将扁平的分类列表构建成树形结构
     *
     * @param categories 待转换的分类列表
     * @return 树形结构的分类列表
     */
    private List<AssistantCategory> buildCategoryTree(List<AssistantCategory> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return new ArrayList<>();
        }
        return categories.stream()
                // 假设根节点的 pid 为 0 或 null
                .filter(category -> category.getPid() == null || category.getPid() == 0)
                .peek(category -> category.setChildren(getChildren(category, categories)))
                .collect(Collectors.toList());
    }

    /**
     * 递归查找子分类
     *
     * @param root  当前分类
     * @param all   所有分类列表
     * @return 子分类列表
     */
    private List<AssistantCategory> getChildren(AssistantCategory root, List<AssistantCategory> all) {
        return all.stream()
                .filter(category -> category.getPid() != null && category.getPid().equals(root.getId()))
                .peek(category -> category.setChildren(getChildren(category, all)))
                .collect(Collectors.toList());
    }
}