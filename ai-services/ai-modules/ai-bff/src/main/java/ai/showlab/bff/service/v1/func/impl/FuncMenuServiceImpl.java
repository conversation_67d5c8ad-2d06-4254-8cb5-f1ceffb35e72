package ai.showlab.bff.service.v1.func.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.func.FuncMenu;
import ai.showlab.bff.mapper.v1.func.FuncMenuMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.func.IFuncMenuService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能菜单服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Service
public class FuncMenuServiceImpl extends BaseService implements IFuncMenuService {

    @Autowired
    private FuncMenuMapper funcMenuMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_MENUS_KEY, key = "#memberId")
    public List<FuncMenu> getMemberMenus(Long memberId) {
        List<FuncMenu> menus = funcMenuMapper.selectMenusByMemberId(memberId);
        return buildMenuTree(menus);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<FuncMenu> getRoleMenus(Long roleId) {
        return funcMenuMapper.selectMenusByRoleId(roleId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<FuncMenu> buildMenuTree(List<FuncMenu> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return new ArrayList<>();
        }
        // 筛选出根节点菜单
        return menus.stream()
                // 假设根节点的 pid 为 0
                .filter(menu -> menu.getPid() == 0)
                .peek(menu -> menu.setChildren(getChildren(menu, menus)))
                .collect(Collectors.toList());
    }

    /**
     * 递归查找子菜单
     *
     * @param root 当前菜单
     * @param all  所有菜单列表
     * @return 子菜单列表
     */
    private List<FuncMenu> getChildren(FuncMenu root, List<FuncMenu> all) {
        return all.stream()
                .filter(menu -> menu.getPid().equals(root.getId()))
                .peek(menu -> menu.setChildren(getChildren(menu, all)))
                .collect(Collectors.toList());
    }
} 