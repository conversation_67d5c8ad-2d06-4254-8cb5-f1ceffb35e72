package ai.showlab.bff.service.v1.billing.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.billing.*;
import ai.showlab.bff.entity.domain.v1.member.Member;
import ai.showlab.bff.entity.dto.v1.BillingUsageDto;
import ai.showlab.bff.entity.vo.v1.BillingOrderVo;
import ai.showlab.bff.entity.vo.v1.BillingTransactionVo;
import ai.showlab.bff.mapper.v1.billing.*;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.billing.IBillingService;
import ai.showlab.bff.service.v1.member.IMemberService;
import ai.showlab.common.core.constant.CacheConstants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * billing 服务
 *
 * <AUTHOR>
 */
@Service
public class BillingServiceImpl extends BaseService implements IBillingService {

    @Autowired
    private BillingPackageMapper packageMapper;
    @Autowired
    private BillingOrderMapper orderMapper;
    @Autowired
    private BillingBalanceMapper balanceMapper;
    @Autowired
    private BillingTransactionMapper transactionMapper;
    @Autowired
    private BillingPriceMapper priceMapper;
    @Autowired
    private BillingUsageMapper usageMapper;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private IMemberService memberService;

    @Override
    @CustomCache(value = CacheConstants.BFF_BILLING_PACKAGES_KEY, key = "'available'", unless = "#result == null or #result.isEmpty()")
    public List<BillingPackage> getAvailablePackages() {
        return packageMapper.selectAvailablePackages();
    }

    @Override
    @Transactional
    public BillingOrder createOrder(Long memberId, Long packageId) {
        BillingPackage pkg = packageMapper.selectPackageById(packageId);
        if (pkg == null) {
            throw new ServiceException("计费套餐不存在");
        }

        BillingOrder order = new BillingOrder();
        order.setOrderNo("ORD_" + UUID.randomUUID().toString().replace("-", ""));
        order.setMemberId(memberId);
        order.setPackageId(packageId);
        order.setAmount(pkg.getPrice());
        order.setCurrencyId(pkg.getCurrencyId());
        // TODO: 1: 待支付
        order.setStatus(1);
        try {
            order.setPackageInfoSnapshot(objectMapper.writeValueAsString(pkg));
        } catch (JsonProcessingException e) {
            throw new ServiceException("创建订单失败：无法序列化套餐快照");
        }
        orderMapper.insertOrder(order);
        return order;
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_MEMBER_BALANCE_KEY, key = "#usageDto.memberId")
    public void recordUsage(BillingUsageDto usageDto) {
        // 1. 获取会员等级
        Integer memberLevel = getMemberLevel(usageDto.getMemberId());

        // 2. 获取计费单价
        BillingPrice price = priceMapper.selectPriceByModelAndLevel(usageDto.getModelId(), memberLevel);
        if (price == null) {
            throw new ServiceException("当前模型或会员等级无可用计费策略");
        }

        // 3. 计算费用
        BigDecimal cost = price.getPrice().multiply(usageDto.getAmount());

        // 4. 检查并扣除余额
        BillingBalance balance = balanceMapper.selectBalanceByMemberId(usageDto.getMemberId());
        if (balance == null || balance.getBalance().compareTo(cost) < 0) {
            throw new ServiceException("余额不足");
        }
        int updated = balanceMapper.updateBalance(usageDto.getMemberId(), cost.negate());
        if (updated == 0) {
            throw new ServiceException("扣除余额失败");
        }

        // 5. 记录用量 (Usage)
        BillingUsage usage = new BillingUsage();
        BeanUtils.copyProperties(usageDto, usage);
        // 假设价格和方案关联
        usage.setPlanId(price.getPlanId());
        usage.setUsedTime(OffsetDateTime.now());
        usageMapper.insertUsage(usage);

        // 6. 记录交易流水 (Transaction)
        BillingTransaction transaction = new BillingTransaction();
        transaction.setMemberId(usageDto.getMemberId());
        // TODO:  1: 消费
        transaction.setType(1);
        transaction.setAmount(cost.negate());
        transaction.setCurrencyId(price.getCurrencyId());
        // 关联用量记录ID
        transaction.setReferenceId(usage.getId());
        transaction.setDescription("使用模型: " + usageDto.getModelId());
        transaction.setTransactionTime(OffsetDateTime.now());
        transactionMapper.insertTransaction(transaction);
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_MEMBER_BALANCE_KEY, key = "#order.memberId")
    public void handlePaymentSuccess(String orderNo, Long paymentGatewayId, String gatewayTransactionId) {
        // 此处需要加锁，防止并发问题。支付回调不一定有用户信息，先查出来
        BillingOrder order = orderMapper.selectOrderByOrderNo(orderNo, null);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        if (order.getStatus() != 1) {
            // 可能已经处理过，直接返回成功
            return;
        }
        // 2: 已完成
        order.setStatus(2);
        order.setPaymentGatewayId(paymentGatewayId);
        order.setGatewayTransactionId(gatewayTransactionId);
        int updated = orderMapper.updateOrderStatus(order);
        if (updated == 0) {
            throw new ServiceException("更新订单状态失败");
        }

        // 增加用户余额
        BillingPackage pkg;
        try {
            pkg = objectMapper.readValue(order.getPackageInfoSnapshot(), BillingPackage.class);
        } catch (JsonProcessingException e) {
            throw new ServiceException("处理支付失败：无法解析套餐快照");
        }
        balanceMapper.updateBalance(order.getMemberId(), pkg.getCreditsGranted());

        // 记录交易流水
        BillingTransaction transaction = new BillingTransaction();
        transaction.setMemberId(order.getMemberId());
        // TODO:  2: 充值
        transaction.setType(2);
        transaction.setAmount(order.getAmount());
        transaction.setCurrencyId(order.getCurrencyId());
        transaction.setReferenceId(order.getId());
        transaction.setDescription("购买套餐: " + pkg.getName());
        transaction.setTransactionTime(OffsetDateTime.now());
        transactionMapper.insertTransaction(transaction);
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_BALANCE_KEY, key = "#memberId")
    public BillingBalance getMemberBalance(Long memberId) {
        BillingBalance balance = balanceMapper.selectBalanceByMemberId(memberId);
        if (balance == null) {
            // 如果用户是第一次查询余额，可能数据库中没有记录，这里初始化一个
            balance = new BillingBalance();
            balance.setMemberId(memberId);
            balance.setBalance(BigDecimal.ZERO);
            balance.setFrozenAmount(BigDecimal.ZERO);
        }
        return balance;
    }

    @Override
    public PageInfo<BillingOrderVo> getMemberOrders(Long memberId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<BillingOrder> orders = orderMapper.selectOrdersByMemberId(memberId);
        PageInfo<BillingOrder> pageInfo = new PageInfo<>(orders);

        List<BillingOrderVo> voList = orders.stream().map(o -> {
            BillingOrderVo vo = new BillingOrderVo();
            BeanUtils.copyProperties(o, vo);
            try {
                BillingPackage pkg = objectMapper.readValue(o.getPackageInfoSnapshot(), BillingPackage.class);
                vo.setPackageName(pkg.getName());
            } catch (JsonProcessingException e) {
                vo.setPackageName("未知套餐");
            }
            vo.setStatusDesc(convertOrderStatus(o.getStatus()));
            return vo;
        }).collect(Collectors.toList());

        PageInfo<BillingOrderVo> voPageInfo = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        return voPageInfo;
    }

    @Override
    public PageInfo<BillingTransactionVo> getMemberTransactions(Long memberId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<BillingTransaction> transactions = transactionMapper.selectTransactionsByMemberId(memberId);
        PageInfo<BillingTransaction> pageInfo = new PageInfo<>(transactions);

        List<BillingTransactionVo> voList = transactions.stream().map(t -> {
            BillingTransactionVo vo = new BillingTransactionVo();
            BeanUtils.copyProperties(t, vo);
            vo.setTypeDesc(convertTransactionType(t.getType()));
            return vo;
        }).collect(Collectors.toList());

        PageInfo<BillingTransactionVo> voPageInfo = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        return voPageInfo;
    }

    private Integer getMemberLevel(Long memberId) {
        // 调用会员服务获取会员信息
        Member member = memberService.getMemberProfileById(memberId);
        if (member == null) {
            throw new ServiceException("会员不存在");
        }
        return member.getMemberLevel();
    }

    private String convertOrderStatus(Integer status) {
        if (status == null) {
            return "未知";
        }
        return switch (status) {
            case 1 -> "待支付";
            case 2 -> "已完成";
            case 3 -> "已取消";
            case 4 -> "支付失败";
            default -> "未知状态";
        };
    }

    private String convertTransactionType(Integer type) {
        if (type == null) {
            return "未知";
        }
        return switch (type) {
            case 1 -> "消费";
            case 2 -> "充值";
            case 3 -> "退款";
            case 4 -> "赠送";
            case 5 -> "分润";
            default -> "未知类型";
        };
    }
} 