package ai.showlab.bff.service.v1.func.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.func.FuncRole;
import ai.showlab.bff.mapper.v1.func.FuncRoleMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.func.IFuncRoleService;
import ai.showlab.common.core.constant.CacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能角色服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Slf4j
@Service
public class FuncRoleServiceImpl extends BaseService implements IFuncRoleService {

    @Autowired
    private FuncRoleMapper funcRoleMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_ROLES_KEY, key = "#memberId", unless = "#result == null or #result.isEmpty()")
    public Set<String> getRoleCodesByMemberId(Long memberId) {
        List<FuncRole> roles = getRolesByMemberId(memberId);
        if (CollectionUtils.isEmpty(roles)) {
            return Set.of();
        }
        return roles.stream().map(FuncRole::getCode).collect(Collectors.toSet());
    }
    
    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_ROLES_KEY, key = "'list:' + #memberId", unless = "#result == null or #result.isEmpty()")
    public List<FuncRole> getRolesByMemberId(Long memberId) {
        return funcRoleMapper.selectRolesByMemberId(memberId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean checkMemberHasRole(Long memberId, String roleCode) {
        return funcRoleMapper.checkMemberHasRole(memberId, roleCode) > 0;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<FuncRole> getAllRoles() {
        return funcRoleMapper.selectAllRoles();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_ROLES_KEY, key = "'default'", unless = "#result == null")
    public FuncRole getDefaultRole() {
        FuncRole role = funcRoleMapper.selectRoleByCode("normal");
        if (role == null) {
            log.warn("默认角色(code='normal')不存在，请检查数据库配置");
        }
        return role;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_ROLES_KEY, key = "'default:id'", unless = "#result == null")
    public Long getDefaultRoleId() {
        FuncRole defaultRole = getDefaultRole();
        return defaultRole != null ? defaultRole.getId() : null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isDefaultRole(String roleCode) {
        return "normal".equals(roleCode);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isDefaultRole(Long roleId) {
        Long defaultRoleId = getDefaultRoleId();
        return defaultRoleId != null && defaultRoleId.equals(roleId);
    }
}