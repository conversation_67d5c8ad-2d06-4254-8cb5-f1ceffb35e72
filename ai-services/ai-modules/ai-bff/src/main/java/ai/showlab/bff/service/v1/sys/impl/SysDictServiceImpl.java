package ai.showlab.bff.service.v1.sys.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.sys.SysDictData;
import ai.showlab.bff.entity.domain.v1.sys.SysDictType;
import ai.showlab.bff.mapper.v1.sys.SysDictDataMapper;
import ai.showlab.bff.mapper.v1.sys.SysDictTypeMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.sys.ISysDictService;
import ai.showlab.common.core.constant.CacheConstants;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class SysDictServiceImpl extends BaseService implements ISysDictService {

    private static final Logger log = LoggerFactory.getLogger(SysDictServiceImpl.class);

    @Autowired
    private SysDictDataMapper dictDataMapper;

    @Autowired
    private SysDictTypeMapper dictTypeMapper;

    /**
     * 项目启动时，预加载字典到缓存
     */
    @PostConstruct
    public void init() {
        loadingDictCache();
    }

    @Override
    @CustomCache(value = CacheConstants.SYS_DICT_KEY, key = "#dictType")
    public List<SysDictData> getDictData(String dictType) {
        log.debug("从数据库加载字典数据: {}", dictType);
        return dictDataMapper.selectDictDataByType(dictType);
    }

    @Override
    public void loadingDictCache() {
        List<SysDictType> dictTypeList = dictTypeMapper.selectAllDictTypes();
        for (SysDictType dictType : dictTypeList) {
            // 通过调用getDictData方法，并利用其@CustomCache注解，将数据加载到缓存中
            getDictData(dictType.getDictType());
        }
        log.info("成功加载 {} 个字典类型到缓存。", dictTypeList.size());
    }

    @Override
    @CacheEvict(value = CacheConstants.SYS_DICT_KEY, allEntries = true)
    public void clearDictCache() {
        log.info("已清空所有字典缓存。");
    }

    @Override
    public void resetDictCache() {
        clearDictCache();
        loadingDictCache();
    }
} 