package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 交易记录视图对象
 * <p>
 * 用于向前端展示交易流水记录，包含关联的货币信息和类型描述。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingTransactionVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 交易类型 (字典: transaction_type), 1-消费, 2-充值, 3-退款, 4-赠送, 5-分润
     */
    private Integer type;

    /**
     * 交易类型描述
     */
    private String typeDesc;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;

    /**
     * 货币代码 (如 CNY, USD)
     */
    private String currencyCode;

    /**
     * 关联ID (如订单ID, 使用记录ID)
     */
    private Long referenceId;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 交易发生时间
     */
    private OffsetDateTime transactionTime;

    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
}