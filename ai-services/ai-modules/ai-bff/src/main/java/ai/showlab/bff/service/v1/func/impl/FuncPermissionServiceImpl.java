package ai.showlab.bff.service.v1.func.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.func.FuncPermission;
import ai.showlab.bff.mapper.v1.func.FuncPermissionMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.func.IFuncPermissionService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能权限服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Service
public class FuncPermissionServiceImpl extends BaseService implements IFuncPermissionService {

    @Autowired
    private FuncPermissionMapper permissionMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_PERMISSIONS_KEY, key = "#memberId", unless = "#result == null or #result.isEmpty()")
    public Set<String> getPermissionCodesByMemberId(Long memberId) {
        List<FuncPermission> permissions = permissionMapper.selectPermissionsByMemberId(memberId);
        if (CollectionUtils.isEmpty(permissions)) {
            return Set.of();
        }
        return permissions.stream().map(FuncPermission::getCode).collect(Collectors.toSet());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean checkMemberHasPermission(Long memberId, String permissionCode) {
        // 可以考虑在这里增加对超级管理员的判断
        return permissionMapper.checkMemberHasPermission(memberId, permissionCode) > 0;
    }
} 