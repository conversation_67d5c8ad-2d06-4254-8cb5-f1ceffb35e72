package ai.showlab.bff.service.v1.assistant;

import ai.showlab.bff.entity.vo.v1.MemberAssistantUsageStatsVo;
import ai.showlab.bff.entity.vo.v1.MemberUsageStatsVo;

/**
 * AI助手使用统计服务接口
 * <p>
 * 定义了AI助手使用统计相关的业务逻辑。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IAssistantUsageStatsService {

    /**
     * 获取会员的助手使用统计
     * <p>
     * 返回会员的总体使用统计，包括使用次数、使用时长等。
     * 此方法会进行缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @return 使用统计VO
     */
    MemberUsageStatsVo getMemberUsageStats(Long memberId);

    /**
     * 获取会员对指定助手的使用统计
     * <p>
     * 返回会员对特定助手的使用统计数据。
     * 此方法会进行缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @param assistantId 助手ID
     * @return 助手使用统计VO
     */
    MemberAssistantUsageStatsVo getAssistantUsageStats(Long memberId, Long assistantId);

    /**
     * 获取会员助手实例的使用统计
     * <p>
     * 返回指定助手实例的使用统计信息。
     * 此方法会进行缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @param memberAssistantId 会员助手实例ID
     * @return 使用统计信息
     */
    MemberAssistantUsageStatsVo getMemberAssistantUsageStats(Long memberId, Long memberAssistantId);

    /**
     * 记录助手使用
     * <p>
     * 记录一次助手使用，更新相关统计数据。
     * </p>
     *
     * @param memberId 会员ID
     * @param assistantId 助手ID
     * @param memberAssistantId 会员助手实例ID（可选）
     */
    void recordAssistantUsage(Long memberId, Long assistantId, Long memberAssistantId);

    /**
     * 刷新会员使用统计缓存
     * <p>
     * 清除指定会员的使用统计缓存。
     * </p>
     *
     * @param memberId 会员ID
     */
    void refreshMemberUsageStatsCache(Long memberId);
}
