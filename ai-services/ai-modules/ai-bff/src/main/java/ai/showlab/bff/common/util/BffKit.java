package ai.showlab.bff.common.util;

import ai.showlab.bff.entity.bo.LoginMember;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;

/**
 * bff 工具类
 * <AUTHOR>
 */
public class BffKit {
    private static final Logger log = LoggerFactory.getLogger(BffKit.class);

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            return normalizeIpAddress(xForwardedFor.split(",")[0].trim());
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return normalizeIpAddress(xRealIp);
        }

        return normalizeIpAddress(request.getRemoteAddr());
    }

    /**
     * 标准化IP地址，将IPv6的本地回环地址转换为IPv4格式
     *
     * @param ip 原始IP地址
     * @return 标准化后的IP地址
     */
    private static String normalizeIpAddress(String ip) {
        if (ip == null) {
            return "unknown";
        }
        // 将IPv6的本地回环地址转换为IPv4格式
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            return "127.0.0.1";
        }
        return ip;
    }

    /**
     * 获取或创建SessionID
     *
     * @param request HTTP请求对象
     * @return SessionID
     */
    public static String getOrCreateSessionId(HttpServletRequest request) {
        try {
            // 如果不存在则创建新session
            HttpSession session = request.getSession(true);
            return session.getId();
        } catch (Exception e) {
            log.warn("无法获取或创建HttpSession，使用UUID作为临时标识: {}", e.getMessage());
            return java.util.UUID.randomUUID().toString();
        }
    }

    /**
     * 生成用户标识符，用于防重复提交Token
     * 优先使用登录用户ID，如果未登录则使用IP地址+SessionID或生成临时UUID
     *
     * @param request HTTP请求对象
     * @return 用户标识符
     */
    public static String generateMemberIdentifier(HttpServletRequest request) {
        try {
            // 尝试获取登录用户ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof LoginMember loginMember) {
                if (loginMember.getMemberId() != null) {
                    log.debug("使用登录用户ID作为防重复提交Token标识: {}", loginMember.getMemberId());
                    return "user:" + loginMember.getMemberId();
                }
            }
        } catch (Exception e) {
            log.debug("无法从SecurityContext获取登录用户信息: {}", e.getMessage());
        }

        // 未登录用户，使用IP地址+SessionID作为标识
        String identifier = "guest:" + getIp(request) + ":" + getOrCreateSessionId(request);
        log.debug("使用访客标识作为防重复提交Token标识: {}", identifier);
        return identifier;
    }

    /**
     * 从 Spring Security 上下文获取当前会员信息
     * <p>
     * 安全地获取当前登录的会员信息，处理匿名用户的情况。
     * </p>
     *
     * @return LoginMember，如果未登录或匿名访问则返回null
     */
    public static LoginMember getLoginMember() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return null;
            }

            Object principal = authentication.getPrincipal();

            // 检查是否为匿名用户
            if (principal instanceof String) {
                // 匿名用户的principal通常是"anonymousUser"字符串
                return null;
            }

            // 检查是否为LoginMember类型
            if (principal instanceof LoginMember) {
                return (LoginMember) principal;
            }

            // 其他类型的principal，返回null
            return null;
        } catch (Exception e) {
            // 发生任何异常都返回null，避免影响业务流程
            return null;
        }
    }

    /**
     * 获取当前登录会员ID
     * <p>
     * 安全地获取当前登录会员的ID，处理未登录的情况。
     * </p>
     *
     * @return 会员ID，如果未登录则返回null
     */
    public static Long getCurrentMemberId() {
        LoginMember member = getLoginMember();
        return member != null ? member.getMemberId() : null;
    }

    /**
     * 检查当前用户是否已登录
     * <p>
     * 判断当前用户是否为已认证的会员用户。
     * </p>
     *
     * @return true表示已登录，false表示未登录或匿名用户
     */
    public static boolean isLoggedIn() {
        return getLoginMember() != null;
    }

    /**
     * 检查当前用户是否为匿名用户
     * <p>
     * 判断当前用户是否为匿名访问。
     * </p>
     *
     * @return true表示匿名用户，false表示已登录用户
     */
    public static boolean isAnonymous() {
        return !isLoggedIn();
    }

}
