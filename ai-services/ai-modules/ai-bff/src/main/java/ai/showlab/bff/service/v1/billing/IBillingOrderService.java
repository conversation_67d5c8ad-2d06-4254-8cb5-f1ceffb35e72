package ai.showlab.bff.service.v1.billing;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.param.BillingOrderCreateParam;
import ai.showlab.bff.entity.param.BillingOrderListParam;
import ai.showlab.bff.entity.vo.v1.BillingOrderVo;
import ai.showlab.common.core.web.page.PageResult;

/**
 * 计费订单服务接口
 * <p>
 * 定义了面向外部用户的订单相关业务逻辑。
 * 包括订单创建、查询、支付处理等功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IBillingOrderService {

    // ==================== 订单管理功能 ====================

    /**
     * 创建订单
     * <p>
     * 用户购买套餐时创建订单，返回订单信息用于后续支付。
     * 会验证套餐可用性、用户权限等。
     * </p>
     *
     * @param requestParams 请求参数，包含套餐ID和支付网关ID
     * @return 创建的订单信息
     */
    BillingOrderVo createOrder(RequestParams<BillingOrderCreateParam> requestParams);

    /**
     * 分页查询当前会员的订单列表
     * <p>
     * 支持按状态、时间范围等条件筛选，返回适合前端展示的VO对象。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 分页结果
     */
    PageResult<BillingOrderVo> getMemberOrderList(RequestParams<BillingOrderListParam> requestParams);

    /**
     * 根据订单号获取订单详情
     * <p>
     * 返回订单的详细信息，包括套餐信息、支付状态等。
     * 只能查询当前会员的订单。
     * </p>
     *
     * @param orderNo 订单号
     * @return 订单详情VO
     */
    BillingOrderVo getOrderDetail(String orderNo);

    /**
     * 取消订单
     * <p>
     * 取消待支付状态的订单。
     * 只能取消当前会员的订单。
     * </p>
     *
     * @param orderNo 订单号
     * @return 是否取消成功
     */
    boolean cancelOrder(String orderNo);

    // ==================== 支付相关功能 ====================

    /**
     * 处理支付成功回调
     * <p>
     * 支付网关回调时调用，更新订单状态并处理后续业务逻辑。
     * 此方法需要保证幂等性。
     * </p>
     *
     * @param orderNo              订单号
     * @param paymentGatewayId     支付网关ID
     * @param gatewayTransactionId 网关交易流水号
     */
    void handlePaymentSuccess(String orderNo, Long paymentGatewayId, String gatewayTransactionId);

    /**
     * 处理支付失败回调
     * <p>
     * 支付失败时调用，更新订单状态。
     * </p>
     *
     * @param orderNo     订单号
     * @param failReason  失败原因
     */
    void handlePaymentFailure(String orderNo, String failReason);

    // ==================== 内部方法 ====================

    /**
     * 根据订单号获取订单信息（内部方法）
     * <p>
     * 用于内部业务逻辑，返回Domain对象。
     * </p>
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    ai.showlab.bff.entity.domain.v1.billing.BillingOrder getOrderByOrderNo(String orderNo);

    /**
     * 检查订单是否属于指定会员
     * <p>
     * 用于权限验证。
     * </p>
     *
     * @param orderNo  订单号
     * @param memberId 会员ID
     * @return 是否属于该会员
     */
    boolean isOrderBelongToMember(String orderNo, Long memberId);
}
