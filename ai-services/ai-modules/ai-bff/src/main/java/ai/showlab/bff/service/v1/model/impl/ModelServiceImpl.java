package ai.showlab.bff.service.v1.model.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.domain.v1.model.Model;
import ai.showlab.bff.entity.param.ModelByCodeParam;
import ai.showlab.bff.entity.param.ModelDetailParam;
import ai.showlab.bff.entity.param.ModelListParam;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.bff.mapper.v1.model.*;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.model.IModelService;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.core.web.page.PageResult;
import ai.showlab.common.protocol.enums.ModelCallStatusEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 模型服务实现类
 * 注意：模型调用状态使用数据字典 model_call_status，对应枚举 {@link ModelCallStatusEnum}：
 * - 1: 成功 (ModelCallStatusEnum.SUCCESS)
 * - 2: 失败 (ModelCallStatusEnum.FAILED)
 * - 3: 处理中 (ModelCallStatusEnum.PENDING)
 * - 4: 超时 (ModelCallStatusEnum.TIMEOUT)
 * <AUTHOR>
 * @date 2024-07-26
 */
@Slf4j
@Service
public class ModelServiceImpl extends BaseService implements IModelService {

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private ModelCategoryMapper modelCategoryMapper;

    @Autowired
    private ModelProviderMapper modelProviderMapper;

    @Autowired
    private ModelFeatureMapper modelFeatureMapper;

    @Autowired
    private ModelOutputFormatMapper modelOutputFormatMapper;

    /**
     * 分页查询模型列表
     * <p>
     * 支持按分类、供应商、特性等条件筛选，返回适合前端展示的VO对象。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 分页结果
     */
    @Override
    public PageResult<ModelListVo> getModelList(RequestParams<ModelListParam> requestParams) {
        try {
            ModelListParam param = requestParams.getBizParam();

            // 查询总数
            long total = modelMapper.countModelsByCondition(param);
            if (total == 0) {
                return PageResult.empty(param.getPageNum(), param.getPageSize());
            }

            // 直接查询VO数据，包含关联信息，无需Java转换
            List<ModelListVo> modelListVos = modelMapper.selectModelListVoByCondition(param);

            return PageResult.of(modelListVos, total, param.getPageNum(), param.getPageSize());
        } catch (Exception e) {
            log.error("分页查询模型列表失败，参数: {}", requestParams.getBizParam(), e);
            throw new BusinessException("查询模型列表失败，请稍后重试");
        }
    }

    /**
     * 根据ID获取模型详情
     * <p>
     * 返回包含完整信息的模型详情，包括特性、输出格式等。
     * 此方法会进行缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含模型ID
     * @return 模型详情VO
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'detail:' + #requestParams.bizParam.modelId")
    public ModelVo getModelDetail(RequestParams<ModelDetailParam> requestParams) {
        try {
            ModelDetailParam param = requestParams.getBizParam();
            Long modelId = param.getModelId();

            // 直接查询ModelVo基础信息（包含分类和供应商）
            ModelVo modelVo = modelMapper.selectModelVoBaseById(modelId);
            if (modelVo == null) {
                // 模型不存在或已禁用
                return null;
            }

            // 直接查询特性和输出格式VO
            List<ModelFeatureVo> features = modelFeatureMapper.selectModelFeatureVosByModelId(modelId);
            List<ModelOutputFormatVo> outputFormats = modelOutputFormatMapper.selectModelOutputFormatVosByModelId(modelId);

            // 设置关联信息
            modelVo.setFeatures(features);
            modelVo.setOutputFormats(outputFormats);

            return modelVo;
        } catch (Exception e) {
            log.error("获取模型详情失败，模型ID: {}", requestParams.getBizParam().getModelId(), e);
            throw new BusinessException("获取模型详情失败，请稍后重试");
        }
    }

    /**
     * 根据编码获取模型信息
     * <p>
     * 用于模型调用前的信息获取，此方法会进行缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含模型编码
     * @return 模型详情VO
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'detail_code:' + #requestParams.bizParam.code")
    public ModelVo getModelByCode(RequestParams<ModelByCodeParam> requestParams) {
        try {
            ModelByCodeParam param = requestParams.getBizParam();
            String code = param.getCode();

            if (!StringUtils.hasText(code)) {
                throw new BusinessException("模型编码不能为空");
            }

            // 直接查询ModelVo基础信息（包含分类和供应商）
            ModelVo modelVo = modelMapper.selectModelVoBaseByCode(code);
            if (modelVo == null) {
                // 模型不存在或已禁用
                return null;
            }

            // 直接查询特性和输出格式VO
            List<ModelFeatureVo> features = modelFeatureMapper.selectModelFeatureVosByModelId(modelVo.getId());
            List<ModelOutputFormatVo> outputFormats = modelOutputFormatMapper.selectModelOutputFormatVosByModelId(modelVo.getId());

            // 设置关联信息
            modelVo.setFeatures(features);
            modelVo.setOutputFormats(outputFormats);

            return modelVo;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("根据编码获取模型信息失败，编码: {}", requestParams.getBizParam().getCode(), e);
            throw new BusinessException("获取模型信息失败，请稍后重试");
        }
    }

    /**
     * 获取热门模型列表
     * <p>
     * 返回热门推荐的模型列表，用于首页展示。
     * 此方法会进行缓存。
     * </p>
     *
     * @return 热门模型列表
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'popular'")
    public List<ModelListVo> getPopularModels() {
        try {
            // 直接查询热门模型VO，包含关联信息，无需Java转换
            return modelMapper.selectPopularModelsVo(10);
        } catch (Exception e) {
            log.error("获取热门模型列表失败", e);
            // 返回空列表而不是抛异常，避免影响首页展示
            return List.of();
        }
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'all'")
    public List<Model> getAllModels() {
        return modelMapper.selectAllModels();
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'id:' + #id")
    public Model getModelById(Long id) {
        return modelMapper.selectModelById(id);
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'code:' + #code")
    public Model getModelByCode(String code) {
        return modelMapper.selectModelByCode(code);
    }

    @Override
    public List<Model> getModelsByCategoryId(Long categoryId) {
        // 直接从缓存的“所有模型”列表中过滤，避免了数据库查询
        return getAllModels().stream()
                .filter(model -> model.getCategoryId().equals(categoryId))
                .collect(Collectors.toList());
    }

    @Override
    public List<Model> getModelsByProviderId(Long providerId) {
        // 同样从缓存中过滤
        return getAllModels().stream()
                .filter(model -> model.getProviderId().equals(providerId))
                .collect(Collectors.toList());
    }

    // ==================== 会员权限相关方法实现 ====================

    /**
     * 获取当前会员可访问的模型列表
     */
    @Override
    public PageResult<ModelListVo> getAccessibleModels(RequestParams<ModelListParam> requestParams) {
        try {
            ModelListParam param = requestParams.getBizParam();
            Long memberId = getCurrentMemberId();

            // 查询总数（包含权限过滤）
            long total = modelMapper.countAccessibleModelsByCondition(param, memberId);
            if (total == 0) {
                return PageResult.empty(param.getPageNum(), param.getPageSize());
            }

            // 查询可访问的模型列表（包含权限过滤）
            List<ModelListVo> modelListVos = modelMapper.selectAccessibleModelListVoByCondition(param, memberId);

            return PageResult.of(modelListVos, total, param.getPageNum(), param.getPageSize());
        } catch (Exception e) {
            log.error("获取可访问模型列表失败，会员ID: {}, 参数: {}", getCurrentMemberId(), requestParams.getBizParam(), e);
            throw new BusinessException("获取可访问模型失败，请稍后重试");
        }
    }

    /**
     * 检查当前会员是否有权限访问指定模型
     */
    @Override
    public boolean checkModelAccess(RequestParams<ModelDetailParam> requestParams) {
        try {
            ModelDetailParam param = requestParams.getBizParam();
            Long memberId = getCurrentMemberId();

            // 调用Mapper检查权限
            return modelMapper.checkMemberModelAccess(memberId, param.getModelId());
        } catch (Exception e) {
            log.error("检查模型访问权限失败，会员ID: {}, 模型ID: {}", getCurrentMemberId(), requestParams.getBizParam().getModelId(), e);
            // 权限检查失败时，为安全起见返回false
            return false;
        }
    }

    // ==================== 会员个性化功能实现 ====================

    /**
     * 获取当前会员收藏的模型列表
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'favorites:' + #getCurrentMemberId()")
    public List<ModelListVo> getFavoriteModels() {
        try {
            Long memberId = getCurrentMemberId();

            // 查询收藏的模型列表
            return modelMapper.selectFavoriteModelsByMemberId(memberId);
        } catch (Exception e) {
            log.error("获取收藏模型列表失败，会员ID: {}", getCurrentMemberId(), e);
            // 返回空列表而不是抛异常
            return List.of();
        }
    }

    /**
     * 切换模型收藏状态
     */
    @Override
    public boolean toggleFavoriteModel(RequestParams<ModelDetailParam> requestParams) {
        try {
            ModelDetailParam param = requestParams.getBizParam();
            Long memberId = getCurrentMemberId();
            Long modelId = param.getModelId();

            // 检查模型是否存在且可访问
            if (!checkModelAccess(requestParams)) {
                throw new BusinessException("无权限访问该模型");
            }

            // 检查当前收藏状态
            boolean isFavorited = modelMapper.isMemberFavoriteModel(memberId, modelId);

            if (isFavorited) {
                // 取消收藏
                modelMapper.removeMemberFavoriteModel(memberId, modelId);
                log.info("会员 {} 取消收藏模型 {}", memberId, modelId);
                return false;
            } else {
                // 添加收藏
                modelMapper.addMemberFavoriteModel(memberId, modelId);
                log.info("会员 {} 收藏模型 {}", memberId, modelId);
                return true;
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("切换模型收藏状态失败，会员ID: {}, 模型ID: {}", getCurrentMemberId(), requestParams.getBizParam().getModelId(), e);
            throw new BusinessException("操作失败，请稍后重试");
        }
    }

    /**
     * 获取当前会员最近使用的模型
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'recent:' + #getCurrentMemberId()")
    public List<ModelListVo> getRecentModels() {
        try {
            Long memberId = getCurrentMemberId();

            // 查询最近使用的模型（限制10个）
            return modelMapper.selectRecentModelsByMemberId(memberId, 10);
        } catch (Exception e) {
            log.error("获取最近使用模型失败，会员ID: {}", getCurrentMemberId(), e);
            // 返回空列表而不是抛异常
            return List.of();
        }
    }

    /**
     * 获取为当前会员推荐的模型
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'recommended:' + #getCurrentMemberId()")
    public List<ModelListVo> getRecommendedModels() {
        try {
            Long memberId = getCurrentMemberId();

            // 基于会员使用习惯和热门度推荐模型（限制10个）
            return modelMapper.selectRecommendedModelsByMemberId(memberId, 10);
        } catch (Exception e) {
            log.error("获取推荐模型失败，会员ID: {}", getCurrentMemberId(), e);
            // 返回空列表而不是抛异常
            return List.of();
        }
    }

    // ==================== 模型状态和监控功能实现 ====================

    /**
     * 检查模型服务状态
     */
    @Override
    public ModelStatusVo checkModelStatus(RequestParams<ModelDetailParam> requestParams) {
        try {
            ModelDetailParam param = requestParams.getBizParam();
            Long modelId = param.getModelId();

            // 检查模型是否存在
            Model model = modelMapper.selectModelById(modelId);
            if (model == null) {
                throw new BusinessException("模型不存在");
            }

            ModelStatusVo status = new ModelStatusVo();
            status.setModelId(modelId);
            status.setModelCode(model.getCode());
            status.setModelName(model.getName());
            status.setIsEnabled(model.getIsEnabled());
            status.setCheckTime(LocalDateTime.now());

            // 检查模型服务可用性（这里可以调用实际的健康检查接口）
            boolean isAvailable = checkModelServiceAvailability(model);
            status.setIsAvailable(isAvailable);

            if (isAvailable) {
                // 获取响应时间等指标
                ModelBasicMetrics metrics = getBasicModelMetrics(model);
                status.setAvgResponseTime(metrics.getAvgResponseTime());
                status.setSuccessRate(metrics.getSuccessRate());
                status.setQps(metrics.getQps());
                status.setLastCheckTime(metrics.getLastCheckTime());
                status.setHealthStatus("健康");
            } else {
                status.setHealthStatus("不可用");
                status.setErrorMessage("模型服务当前不可用");
            }

            return status;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("检查模型状态失败，模型ID: {}", requestParams.getBizParam().getModelId(), e);
            throw new BusinessException("检查模型状态失败，请稍后重试");
        }
    }

    /**
     * 获取模型性能指标
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'metrics:' + #requestParams.bizParam.modelId")
    public ModelPerformanceMetricsVo getModelMetrics(RequestParams<ModelDetailParam> requestParams) {
        try {
            ModelDetailParam param = requestParams.getBizParam();
            Long modelId = param.getModelId();

            // 检查模型是否存在
            Model model = modelMapper.selectModelById(modelId);
            if (model == null) {
                throw new BusinessException("模型不存在");
            }

            // 从调用日志表获取性能指标
            ModelPerformanceMetricsVo metrics = modelMapper.selectModelPerformanceMetrics(modelId);

            // 设置基础信息
            if (metrics != null) {
                metrics.setModelId(modelId);
                metrics.setModelCode(model.getCode());
                metrics.setModelName(model.getName());
                metrics.setMetricsTime(LocalDateTime.now());
            }

            return metrics;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取模型性能指标失败，模型ID: {}", requestParams.getBizParam().getModelId(), e);
            throw new BusinessException("获取模型性能指标失败，请稍后重试");
        }
    }

    // ==================== 使用统计功能实现 ====================

    /**
     * 获取当前会员的模型使用统计
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'member_stats:' + #getCurrentMemberId()")
    public MemberUsageStatsVo getMemberUsageStats() {
        try {
            Long memberId = getCurrentMemberId();

            // 从调用日志表获取会员使用统计
            MemberUsageStatsVo stats = modelMapper.selectMemberUsageStats(memberId);

            // 设置基础信息
            if (stats != null) {
                stats.setMemberId(memberId);
                stats.setStatsTime(OffsetDateTime.now());
            }

            return stats;
        } catch (Exception e) {
            log.error("获取会员使用统计失败，会员ID: {}", getCurrentMemberId(), e);
            throw new BusinessException("获取使用统计失败，请稍后重试");
        }
    }

    /**
     * 获取当前会员对指定模型的使用统计
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODELS_KEY, key = "'model_stats:' + #getCurrentMemberId() + ':' + #requestParams.bizParam.modelId")
    public MemberModelUsageStatsVo getModelUsageStats(RequestParams<ModelDetailParam> requestParams) {
        try {
            ModelDetailParam param = requestParams.getBizParam();
            Long memberId = getCurrentMemberId();
            Long modelId = param.getModelId();

            // 检查模型是否存在
            Model model = modelMapper.selectModelById(modelId);
            if (model == null) {
                throw new BusinessException("模型不存在");
            }

            // 从调用日志表获取会员对特定模型的使用统计
            MemberModelUsageStatsVo stats = modelMapper.selectMemberModelUsageStats(memberId, modelId);

            // 设置基础信息
            if (stats != null) {
                stats.setMemberId(memberId);
                stats.setModelId(modelId);
                stats.setModelCode(model.getCode());
                stats.setModelName(model.getName());
                stats.setStatsTime(LocalDateTime.now());
            }

            return stats;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取模型使用统计失败，会员ID: {}, 模型ID: {}", getCurrentMemberId(), requestParams.getBizParam().getModelId(), e);
            throw new BusinessException("获取模型使用统计失败，请稍后重试");
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 检查模型服务可用性
     */
    private boolean checkModelServiceAvailability(Model model) {
        try {
            // TODO: 这里应该调用实际的模型服务健康检查接口
            // 目前简化为检查模型是否启用
            return Boolean.TRUE.equals(model.getIsEnabled());
        } catch (Exception e) {
            log.warn("检查模型服务可用性失败，模型ID: {}", model.getId(), e);
            return false;
        }
    }

    /**
     * 获取基础模型指标
     */
    private ModelBasicMetrics getBasicModelMetrics(Model model) {
        try {
            // TODO: 这里应该调用实际的模型服务获取指标
            // 目前返回模拟数据
            ModelBasicMetrics metrics = new ModelBasicMetrics();
            metrics.setAvgResponseTime(new BigDecimal("1500"));
            metrics.setSuccessRate(new BigDecimal("98.5"));
            metrics.setQps(new BigDecimal("10.5"));
            metrics.setLastCheckTime(LocalDateTime.now());
            return metrics;
        } catch (Exception e) {
            log.warn("获取基础模型指标失败，模型ID: {}", model.getId(), e);
            return new ModelBasicMetrics();
        }
    }

    /**
     * 基础模型指标内部类
     */
    @Data
    private static class ModelBasicMetrics {
        /**
         * 平均响应时间(ms)
         */
        private BigDecimal avgResponseTime;
        /**
         * 成功率
         */
        private BigDecimal successRate;
        /**
         * 每秒查询数
         */
        private BigDecimal qps;
        /**
         * 最近检测时间
         */
        private LocalDateTime lastCheckTime;
    }
}