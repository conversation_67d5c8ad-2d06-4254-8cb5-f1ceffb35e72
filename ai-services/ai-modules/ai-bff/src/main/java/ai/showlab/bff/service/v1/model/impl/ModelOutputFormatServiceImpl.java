package ai.showlab.bff.service.v1.model.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.model.ModelOutputFormat;
import ai.showlab.bff.mapper.v1.model.ModelOutputFormatMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.model.IModelOutputFormatService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型输出格式服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class ModelOutputFormatServiceImpl extends BaseService implements IModelOutputFormatService {

    @Autowired
    private ModelOutputFormatMapper modelOutputFormatMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_MODEL_OUTPUT_FORMATS_KEY, key = "#modelId")
    public List<ModelOutputFormat> getOutputFormatsByModelId(Long modelId) {
        return modelOutputFormatMapper.selectModelOutputFormatsByModelId(modelId);
    }
} 