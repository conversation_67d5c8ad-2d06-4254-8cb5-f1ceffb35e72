package ai.showlab.bff.service.v1.billing;

import ai.showlab.bff.entity.vo.v1.BillingBalanceVo;

import java.math.BigDecimal;

/**
 * 计费余额服务接口
 * <p>
 * 定义了面向外部用户的余额相关业务逻辑。
 * 包括余额查询、余额变动等功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IBillingBalanceService {

    // ==================== 余额查询功能 ====================

    /**
     * 获取当前会员的余额信息
     * <p>
     * 返回包含可用余额、冻结金额等信息的VO对象。
     * 此方法会进行缓存以提高性能。
     * </p>
     *
     * @return 余额信息VO
     */
    BillingBalanceVo getCurrentMemberBalance();

    /**
     * 获取指定会员的余额信息
     * <p>
     * 返回包含可用余额、冻结金额等信息的VO对象。
     * 此方法会进行缓存以提高性能。
     * </p>
     *
     * @param memberId 会员ID
     * @return 余额信息VO
     */
    BillingBalanceVo getMemberBalance(Long memberId);

    /**
     * 检查当前会员余额是否充足
     * <p>
     * 用于在消费前验证余额是否足够。
     * </p>
     *
     * @param amount 需要消费的金额
     * @return 余额是否充足
     */
    boolean isBalanceSufficient(BigDecimal amount);

    /**
     * 检查指定会员余额是否充足
     * <p>
     * 用于在消费前验证余额是否足够。
     * </p>
     *
     * @param memberId 会员ID
     * @param amount   需要消费的金额
     * @return 余额是否充足
     */
    boolean isBalanceSufficient(Long memberId, BigDecimal amount);

    // ==================== 余额变动功能 ====================

    /**
     * 增加会员余额
     * <p>
     * 用于充值、赠送等场景。
     * 会记录交易流水。
     * </p>
     *
     * @param memberId    会员ID
     * @param amount      增加金额
     * @param referenceId 关联ID（如订单ID）
     * @param description 描述
     * @return 是否操作成功
     */
    boolean addBalance(Long memberId, BigDecimal amount, Long referenceId, String description);

    /**
     * 扣除会员余额
     * <p>
     * 用于消费等场景。
     * 会记录交易流水。
     * </p>
     *
     * @param memberId    会员ID
     * @param amount      扣除金额
     * @param referenceId 关联ID（如使用记录ID）
     * @param description 描述
     * @return 是否操作成功
     */
    boolean deductBalance(Long memberId, BigDecimal amount, Long referenceId, String description);

    /**
     * 冻结会员余额
     * <p>
     * 用于预扣费等场景。
     * </p>
     *
     * @param memberId 会员ID
     * @param amount   冻结金额
     * @return 是否操作成功
     */
    boolean freezeBalance(Long memberId, BigDecimal amount);

    /**
     * 解冻会员余额
     * <p>
     * 释放冻结的金额。
     * </p>
     *
     * @param memberId 会员ID
     * @param amount   解冻金额
     * @return 是否操作成功
     */
    boolean unfreezeBalance(Long memberId, BigDecimal amount);

    // ==================== 内部方法 ====================

    /**
     * 初始化会员余额账户
     * <p>
     * 为新会员创建余额记录。
     * </p>
     *
     * @param memberId   会员ID
     * @param currencyId 货币ID
     * @return 是否初始化成功
     */
    boolean initMemberBalance(Long memberId, Long currencyId);

    /**
     * 获取会员余额（内部方法）
     * <p>
     * 返回Domain对象，用于内部业务逻辑。
     * </p>
     *
     * @param memberId 会员ID
     * @return 余额信息
     */
    ai.showlab.bff.entity.domain.v1.billing.BillingBalance getMemberBalanceDomain(Long memberId);
}
