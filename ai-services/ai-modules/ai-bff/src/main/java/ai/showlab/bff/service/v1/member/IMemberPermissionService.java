package ai.showlab.bff.service.v1.member;

import ai.showlab.bff.entity.domain.v1.member.MemberPermission;

import java.util.List;
import java.util.Set;

/**
 * 会员权限服务接口
 * <p>
 * 定义了管理会员权限的业务逻辑，包括直接权限和角色权限的查询和校验。
 * 整合了原有的会员直接权限管理功能和新的权限校验功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IMemberPermissionService {

    // ==================== 原有接口（保持兼容性） ====================

    /**
     * 根据会员ID获取其拥有的所有直接权限ID
     * <p>
     * 此方法会进行缓存，以加速权限校验。
     * </p>
     *
     * @param memberId 会员ID
     * @return 权限ID的 Set 集合
     */
    Set<Long> getPermissionIdsByMemberId(Long memberId);

    /**
     * 检查会员是否拥有指定的直接权限
     *
     * @param memberId     会员ID
     * @param permissionId 权限ID
     * @return true 如果拥有，否则 false
     */
    boolean hasPermission(Long memberId, Long permissionId);

    /**
     * 为会员授予一项特定权限
     * <p>
     * 此方法会清除对应会员的权限缓存。
     * </p>
     *
     * @param permission 包含授权信息的对象
     */
    void grantPermissionToMember(MemberPermission permission);

    /**
     * 撤销会员的一项特定权限
     * <p>
     * 此方法会清除对应会员的权限缓存。
     * </p>
     *
     * @param memberId     会员ID
     * @param permissionId 权限ID
     */
    void revokePermissionFromMember(Long memberId, Long permissionId);

    // ==================== 新增接口（权限校验功能） ====================

    /**
     * 检查会员是否拥有指定权限（通过权限编码）
     * <p>
     * 权限校验逻辑：
     * 1. 首先通过角色权限进行校验（a_member_role -> a_func_role_permission）
     * 2. 如果角色权限校验失败，再通过会员直接权限进行校验（a_member_permission）
     * 3. 会员直接权限可以覆盖角色权限（授予或拒绝）
     * </p>
     *
     * @param memberId 会员ID
     * @param permissionCode 权限编码（对应a_func_permission.code）
     * @return true表示有权限，false表示无权限
     */
    boolean hasPermissionByCode(Long memberId, String permissionCode);

    /**
     * 检查会员是否拥有指定角色的权限
     * <p>
     * 仅通过角色权限进行校验，不考虑会员直接权限。
     * </p>
     *
     * @param memberId 会员ID
     * @param permissionCode 权限编码
     * @return true表示有权限，false表示无权限
     */
    boolean hasRolePermission(Long memberId, String permissionCode);

    /**
     * 检查会员是否拥有直接权限（通过权限编码）
     * <p>
     * 仅通过会员直接权限进行校验，不考虑角色权限。
     * </p>
     *
     * @param memberId 会员ID
     * @param permissionCode 权限编码
     * @return true表示有权限，false表示无权限，null表示未设置直接权限
     */
    Boolean hasMemberPermissionByCode(Long memberId, String permissionCode);

    /**
     * 获取会员的所有权限编码列表
     * <p>
     * 返回会员通过角色和直接授权获得的所有权限编码。
     * </p>
     *
     * @param memberId 会员ID
     * @return 权限编码列表
     */
    List<String> getMemberPermissionCodes(Long memberId);

    /**
     * 获取会员的所有角色编码列表
     * <p>
     * 返回会员拥有的所有角色编码。
     * </p>
     *
     * @param memberId 会员ID
     * @return 角色编码列表
     */
    List<String> getMemberRoleCodes(Long memberId);

    /**
     * 刷新会员权限缓存
     * <p>
     * 当会员的角色或权限发生变化时，调用此方法清除缓存。
     * </p>
     *
     * @param memberId 会员ID
     */
    void refreshMemberPermissionCache(Long memberId);

    /**
     * 检查权限编码是否存在
     * <p>
     * 验证指定的权限编码是否在系统中定义。
     * </p>
     *
     * @param permissionCode 权限编码
     * @return true表示存在，false表示不存在
     */
    boolean isPermissionCodeExists(String permissionCode);

    /**
     * 检查会员是否拥有指定角色
     * <p>
     * 查询a_member_role表，检查会员是否拥有指定角色。
     * </p>
     *
     * @param memberId 会员ID
     * @param roleCode 角色编码
     * @return true表示拥有该角色，false表示没有
     */
    boolean hasMemberRole(Long memberId, String roleCode);

    /**
     * 检查角色是否拥有指定权限
     * <p>
     * 查询a_func_role表，检查角色是否拥有指定权限。
     * </p>
     *
     * @param roleCode 角色编码
     * @param permissionCode 权限编码
     * @return true表示拥有该权限，false表示没有
     */
    boolean hasRolePermissionByCode(String roleCode, String permissionCode);
}