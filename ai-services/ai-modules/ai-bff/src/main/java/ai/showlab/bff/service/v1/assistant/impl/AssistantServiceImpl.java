package ai.showlab.bff.service.v1.assistant.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.common.util.DictUtils;
import ai.showlab.bff.entity.domain.v1.assistant.Assistant;
import ai.showlab.bff.entity.domain.v1.assistant.AssistantParam;
import ai.showlab.bff.entity.param.AssistantByCodeParam;
import ai.showlab.bff.entity.param.AssistantDetailParam;
import ai.showlab.bff.entity.param.AssistantListParam;
import ai.showlab.bff.entity.vo.v1.AssistantDetailVo;
import ai.showlab.bff.entity.vo.v1.AssistantListVo;
import ai.showlab.bff.entity.vo.v1.MemberAssistantUsageStatsVo;
import ai.showlab.bff.entity.vo.v1.MemberUsageStatsVo;
import ai.showlab.bff.mapper.v1.assistant.AssistantMapper;
import ai.showlab.bff.mapper.v1.assistant.AssistantParamMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.assistant.IAssistantService;
import ai.showlab.bff.service.v1.assistant.IAssistantUsageStatsService;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.core.constant.CodeConstants;
import ai.showlab.common.core.web.page.PageResult;
import com.ruoyi.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * AI助手服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Slf4j
@Service
public class AssistantServiceImpl extends BaseService implements IAssistantService {

    private static final String ASSISTANTS_CACHE_KEY = "'" + CacheConstants.BFF_ASSISTANTS_KEY + "'";

    @Autowired
    private AssistantMapper assistantMapper;

    @Autowired
    private AssistantParamMapper assistantParamMapper;

    @Autowired
    private IAssistantUsageStatsService usageStatsService;

    @Autowired
    private DictUtils dictUtils;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_ASSISTANTS_KEY, key = "'public'", unless = "#result == null or #result.isEmpty()")
    public List<Assistant> getPublicAssistants() {
        return assistantMapper.selectPublicAssistants();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_ASSISTANTS_KEY, key = "#memberId", unless = "#result == null or #result.isEmpty()")
    public List<Assistant> getMemberAssistants(Long memberId) {
        return assistantMapper.selectAssistantsByMemberId(memberId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_ASSISTANTS_KEY, key = "#assistantId")
    public Assistant getAssistantDetail(Long assistantId) {
        Assistant assistant = assistantMapper.selectAssistantById(assistantId);
        if (assistant == null) {
            throw new ServiceException("AI助手不存在");
        }
        // 获取并设置助手参数
        List<AssistantParam> params = assistantParamMapper.selectParamsByAssistantId(assistantId);
        assistant.setParams(params);
        return assistant;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void recordAssistantUsage(Long assistantId) {
        try {
            assistantMapper.incrementUsageCount(assistantId);
        } catch (Exception e) {
            // 记录使用次数失败不应该影响主要业务流程
            log.warn("记录助手使用次数失败，assistantId: {}", assistantId, e);
        }
    }

    // ==================== 新增接口实现 ====================

    /**
     * {@inheritDoc}
     */
    @Override
    public PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws BusinessException {
        return executeServiceOperation(() -> {
            AssistantListParam param = requestParams.getBizParam();
            if (param == null) {
                param = new AssistantListParam();
            }

            // 查询数据
            List<AssistantListVo> list = assistantMapper.selectAssistantListVoByCondition(param);
            long total = assistantMapper.countAssistantsByCondition(param);

            // 设置字典显示名称
            if (!CollectionUtils.isEmpty(list)) {
                dictUtils.setInteractionModeNames(list);
                dictUtils.setAssistantStatusNames(list);
            }

            // TODO: 设置当前会员的收藏状态和添加状态
            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId != null && !CollectionUtils.isEmpty(list)) {
                setMemberRelatedFlags(list, currentMemberId);
            }

            return PageResult.of(list, total, param.getPageNum(), param.getPageSize());
        }, "获取助手列表失败");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AssistantDetailVo getAssistantDetail(RequestParams<AssistantDetailParam> requestParams) {
        try {
            AssistantDetailParam param = requestParams.getBizParam();
            Long assistantId = param.getAssistantId();

            AssistantDetailVo detail = assistantMapper.selectAssistantDetailVoById(assistantId);
            if (detail == null) {
                throw new BusinessException(CodeConstants.NOT_FOUND, "助手不存在");
            }

            // 设置字典显示名称
            detail.setInteractionModeName(dictUtils.getInteractionModeName(detail.getInteractionMode()));
            detail.setStatusName(dictUtils.getAssistantStatusName(detail.getStatus()));

            // TODO: 设置当前会员的收藏状态和添加状态
            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId != null) {
                setMemberRelatedFlags(detail, currentMemberId);
            }

            return detail;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取助手详情失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "获取助手详情失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AssistantDetailVo getAssistantByCode(RequestParams<AssistantByCodeParam> requestParams) {
        try {
            AssistantByCodeParam param = requestParams.getBizParam();
            String code = param.getCode();

            AssistantDetailVo detail = assistantMapper.selectAssistantDetailVoByCode(code);
            if (detail == null) {
                throw new BusinessException(CodeConstants.NOT_FOUND, "助手不存在");
            }

            // 设置字典显示名称
            detail.setInteractionModeName(dictUtils.getInteractionModeName(detail.getInteractionMode()));
            detail.setStatusName(dictUtils.getAssistantStatusName(detail.getStatus()));

            // TODO: 设置当前会员的收藏状态和添加状态
            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId != null) {
                setMemberRelatedFlags(detail, currentMemberId);
            }

            return detail;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("根据编码获取助手失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "获取助手信息失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_ASSISTANTS_KEY, key = "'popular'", unless = "#result == null or #result.isEmpty()")
    public List<AssistantListVo> getPopularAssistants() {
        try {
            List<AssistantListVo> list = assistantMapper.selectPopularAssistantsVo(10);

            // 设置字典显示名称
            if (!CollectionUtils.isEmpty(list)) {
                dictUtils.setInteractionModeNames(list);
                dictUtils.setAssistantStatusNames(list);
            }

            // TODO: 设置当前会员的收藏状态和添加状态
            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId != null && !CollectionUtils.isEmpty(list)) {
                setMemberRelatedFlags(list, currentMemberId);
            }

            return list != null ? list : new ArrayList<>();
        } catch (Exception e) {
            log.error("获取热门助手失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PageResult<AssistantListVo> getAccessibleAssistants(RequestParams<AssistantListParam> requestParams) {
        try {
            AssistantListParam param = requestParams.getBizParam();
            if (param == null) {
                param = new AssistantListParam();
            }

            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // 查询会员可访问的助手
            List<AssistantListVo> list = assistantMapper.selectAccessibleAssistantsVo(param, currentMemberId);
            long total = assistantMapper.countAccessibleAssistants(param, currentMemberId);

            // 设置字典显示名称
            if (!CollectionUtils.isEmpty(list)) {
                dictUtils.setInteractionModeNames(list);
                dictUtils.setAssistantStatusNames(list);
            }

            // 设置会员相关标识
            if (!CollectionUtils.isEmpty(list)) {
                setMemberRelatedFlags(list, currentMemberId);
            }

            return PageResult.of(list, total, param.getPageNum(), param.getPageSize());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取可访问助手列表失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "获取助手列表失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean checkAssistantAccess(RequestParams<AssistantDetailParam> requestParams) {
        try {
            AssistantDetailParam param = requestParams.getBizParam();
            Long assistantId = param.getAssistantId();
            Long currentMemberId = BffKit.getCurrentMemberId();

            if (currentMemberId == null) {
                return false;
            }

            // TODO: 实现权限检查逻辑
            // 这里应该根据助手的可见性规则、会员等级、地区等进行权限验证
            Assistant assistant = assistantMapper.selectAssistantById(assistantId);
            if (assistant == null) {
                return false;
            }

            // 简单实现：公开助手都可以访问
            return assistant.getIsPublic() != null && assistant.getIsPublic();
        } catch (Exception e) {
            log.error("检查助手访问权限失败", e);
            return false;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<AssistantListVo> getFavoriteAssistants() {
        try {
            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            List<AssistantListVo> list = assistantMapper.selectFavoriteAssistantsVo(currentMemberId);

            // 设置字典显示名称
            if (!CollectionUtils.isEmpty(list)) {
                dictUtils.setInteractionModeNames(list);
                dictUtils.setAssistantStatusNames(list);
            }

            return list != null ? list : new ArrayList<>();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取收藏助手失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "获取收藏助手失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void favoriteAssistant(RequestParams<AssistantDetailParam> requestParams) {
        try {
            AssistantDetailParam param = requestParams.getBizParam();
            Long assistantId = param.getAssistantId();
            Long currentMemberId = BffKit.getCurrentMemberId();

            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // TODO: 实现收藏逻辑
            // 这里应该在会员收藏表中添加记录
            log.info("会员{}收藏助手{}", currentMemberId, assistantId);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("收藏助手失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "收藏助手失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void unfavoriteAssistant(RequestParams<AssistantDetailParam> requestParams) {
        try {
            AssistantDetailParam param = requestParams.getBizParam();
            Long assistantId = param.getAssistantId();
            Long currentMemberId = BffKit.getCurrentMemberId();

            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // TODO: 实现取消收藏逻辑
            // 这里应该在会员收藏表中删除记录
            log.info("会员{}取消收藏助手{}", currentMemberId, assistantId);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("取消收藏助手失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "取消收藏助手失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberUsageStatsVo getMemberUsageStats() {
        try {
            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }
            return usageStatsService.getMemberUsageStats(currentMemberId);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取会员使用统计失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "获取使用统计失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberAssistantUsageStatsVo getAssistantUsageStats(RequestParams<AssistantDetailParam> requestParams) {
        try {
            AssistantDetailParam param = requestParams.getBizParam();
            Long assistantId = param.getAssistantId();
            Long currentMemberId = BffKit.getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }
            return usageStatsService.getAssistantUsageStats(currentMemberId, assistantId);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取助手使用统计失败", e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, "获取助手使用统计失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<Assistant> getAllAssistants() {
        try {
            return assistantMapper.selectPublicAssistants();
        } catch (Exception e) {
            log.error("获取所有助手失败", e);
            return new ArrayList<>();
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 设置会员相关标识（收藏状态、添加状态等）
     *
     * @param list     助手列表
     * @param memberId 会员ID
     */
    private void setMemberRelatedFlags(List<AssistantListVo> list, Long memberId) {
        // TODO: 实现设置会员相关标识的逻辑
        // 这里应该查询会员的收藏记录和助手实例记录，设置相应的标识
        for (AssistantListVo vo : list) {
            vo.setIsAdded(false);
            vo.setIsFavorited(false);
        }
    }

    /**
     * 设置会员相关标识（收藏状态、添加状态等）
     *
     * @param detail   助手详情
     * @param memberId 会员ID
     */
    private void setMemberRelatedFlags(AssistantDetailVo detail, Long memberId) {
        // TODO: 实现设置会员相关标识的逻辑
        // 这里应该查询会员的收藏记录和助手实例记录，设置相应的标识
        detail.setIsAdded(false);
        detail.setIsFavorited(false);
        detail.setMemberAssistantId(null);
    }
}