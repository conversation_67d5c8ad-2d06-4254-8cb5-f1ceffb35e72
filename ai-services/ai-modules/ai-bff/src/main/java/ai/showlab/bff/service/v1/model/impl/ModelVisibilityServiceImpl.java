package ai.showlab.bff.service.v1.model.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.model.ModelVisibility;
import ai.showlab.bff.mapper.v1.model.ModelVisibilityMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.model.IModelVisibilityService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型可见性服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class ModelVisibilityServiceImpl extends BaseService implements IModelVisibilityService {

    @Autowired
    private ModelVisibilityMapper modelVisibilityMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_MODEL_VISIBILITY_KEY, key = "#modelId")
    public List<ModelVisibility> getVisibilitiesByModelId(Long modelId) {
        return modelVisibilityMapper.selectModelVisibilitiesByModelId(modelId);
    }
} 