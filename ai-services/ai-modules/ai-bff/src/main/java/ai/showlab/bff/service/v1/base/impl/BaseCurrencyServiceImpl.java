package ai.showlab.bff.service.v1.base.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.base.BaseCurrency;
import ai.showlab.bff.mapper.v1.base.BaseCurrencyMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.base.IBaseCurrencyService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 货币表 服务实现层
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class BaseCurrencyServiceImpl extends BaseService implements IBaseCurrencyService {

    @Autowired
    private BaseCurrencyMapper currencyMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_CURRENCY_KEY, key = "'all'")
    public List<BaseCurrency> getAllCurrencies() {
        return currencyMapper.selectAllCurrencies();
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_CURRENCY_KEY, key = "#id")
    public BaseCurrency getCurrencyById(Long id) {
        return currencyMapper.selectCurrencyById(id);
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_CURRENCY_KEY, key = "'default'")
    public BaseCurrency getDefaultCurrency() {
        return currencyMapper.selectDefaultCurrency();
    }
} 