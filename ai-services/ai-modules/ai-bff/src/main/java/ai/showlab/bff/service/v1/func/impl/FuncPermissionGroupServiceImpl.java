package ai.showlab.bff.service.v1.func.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.func.FuncPermissionGroup;
import ai.showlab.bff.mapper.v1.func.FuncPermissionGroupMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.func.IFuncPermissionGroupService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限组服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Service
public class FuncPermissionGroupServiceImpl extends BaseService implements IFuncPermissionGroupService {

    @Autowired
    private FuncPermissionGroupMapper permissionGroupMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_PERMISSION_GROUPS_KEY, unless = "#result == null or #result.isEmpty()")
    public List<FuncPermissionGroup> getAllPermissionGroupsAsTree() {
        List<FuncPermissionGroup> allGroups = permissionGroupMapper.selectAllPermissionGroups();
        return buildGroupTree(allGroups);
    }

    /**
     * 将扁平的权限组列表构建成树形结构
     *
     * @param groups 待转换的权限组列表
     * @return 树形结构的权限组列表
     */
    private List<FuncPermissionGroup> buildGroupTree(List<FuncPermissionGroup> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return new ArrayList<>();
        }
        return groups.stream()
                // 假设根节点的 pid 为 0
                .filter(group -> group.getPid() == 0)
                .peek(group -> group.setChildren(getChildren(group, groups)))
                .collect(Collectors.toList());
    }

    /**
     * 递归查找子权限组
     *
     * @param root 当前权限组
     * @param all  所有权限组列表
     * @return 子权限组列表
     */
    private List<FuncPermissionGroup> getChildren(FuncPermissionGroup root, List<FuncPermissionGroup> all) {
        return all.stream()
                .filter(group -> group.getPid().equals(root.getId()))
                .peek(group -> group.setChildren(getChildren(group, all)))
                .collect(Collectors.toList());
    }
} 