package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 助手列表视图对象
 * 用于向前端展示助手列表信息
 * 
 * <AUTHOR>
 */
@Data
public class AssistantListVo {
    
    /**
     * 助手ID
     */
    private Long id;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 助手编码
     */
    private String code;
    
    /**
     * 助手名称
     */
    private String name;
    
    /**
     * 助手描述
     */
    private String description;
    
    /**
     * 助手图标URL
     */
    private String iconUrl;
    
    /**
     * 交互模式 (字典: assistant_interaction_mode)
     * 1-标准对话, 2-图像分析, 3-文生视频, 4-持续监听
     */
    private Integer interactionMode;

    /**
     * 交互模式名称（通过数据字典获取）
     */
    private String interactionModeName;

    /**
     * 助手状态 (字典: assistant_status)
     * 1-草稿, 2-待审核, 3-已发布, 4-已归档
     */
    private Integer status;

    /**
     * 助手状态名称（通过数据字典获取）
     */
    private String statusName;
    
    /**
     * 使用次数
     */
    private Long usageCount;
    
    /**
     * 是否公开
     */
    private Boolean isPublic;
    
    /**
     * 是否为预设助手
     */
    private Boolean isPreset;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
    
    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
    
    /**
     * 是否已被当前会员添加
     */
    private Boolean isAdded;
    
    /**
     * 是否被当前会员收藏
     */
    private Boolean isFavorited;
}
