package ai.showlab.bff.service.v1.assistant;

import ai.showlab.bff.entity.domain.v1.assistant.AssistantCategory;
import ai.showlab.bff.entity.vo.v1.AssistantCategoryVo;

import java.util.List;

/**
 * AI助手分类服务接口
 * <p>
 * 定义了AI助手分类相关的业务逻辑。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IAssistantCategoryService {

    /**
     * 获取所有AI助手分类，并以树形结构返回（VO对象）
     * <p>
     * 此方法会缓存结果以提高性能。
     * 返回适合前端展示的VO对象，包含助手数量等统计信息。
     * </p>
     *
     * @return 分类树形列表
     */
    List<AssistantCategoryVo> getAllCategoriesAsTree();

    /**
     * 获取所有AI助手分类，并以树形结构返回（Domain对象）
     * <p>
     * 此方法会缓存结果以提高性能。
     * 主要用于内部业务逻辑。
     * </p>
     *
     * @return 分类树形列表
     */
    List<AssistantCategory> getAllCategoriesAsTreeDomain();

}