package ai.showlab.bff.common.docs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 计费相关API文档注解
 * <p>
 * 为计费相关的接口提供统一的API文档注解。
 * </p>
 *
 * <AUTHOR>
 */
public class BillingApiAnnotations {

    // ==================== 套餐相关API ====================

    /**
     * 获取套餐列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取计费套餐列表",
            description = "分页查询可用的计费套餐，支持按类型、价格范围、关键词等条件筛选"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetPackageListApiDoc {
    }

    /**
     * 获取套餐详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取套餐详情",
            description = "根据套餐ID获取详细信息，包括价格、授予积分、有效期等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "套餐不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetPackageDetailApiDoc {
    }

    /**
     * 获取热门套餐API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取热门套餐列表",
            description = "获取热门推荐的套餐列表，用于首页展示"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetPopularPackagesApiDoc {
    }

    /**
     * 获取推荐套餐API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取推荐套餐列表",
            description = "根据当前会员的使用情况和等级推荐合适的套餐"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetRecommendedPackagesApiDoc {
    }

    // ==================== 订单相关API ====================

    /**
     * 创建订单API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "创建订单",
            description = "购买套餐时创建订单，返回订单信息用于后续支付"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "套餐不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface CreateOrderApiDoc {
    }

    /**
     * 获取订单列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取我的订单列表",
            description = "分页查询当前会员的订单历史，支持按状态、时间范围等条件筛选"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetOrderListApiDoc {
    }

    /**
     * 获取订单详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取订单详情",
            description = "根据订单号获取订单的详细信息，包括套餐信息、支付状态等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "订单不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetOrderDetailApiDoc {
    }

    /**
     * 取消订单API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "取消订单",
            description = "取消待支付状态的订单"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "取消成功"),
            @ApiResponse(responseCode = "400", description = "参数错误或订单状态不允许取消"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "订单不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface CancelOrderApiDoc {
    }

    // ==================== 余额相关API ====================

    /**
     * 获取余额信息API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取我的余额信息",
            description = "获取当前会员的余额信息，包括可用余额、冻结金额等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetBalanceApiDoc {
    }

    // ==================== 交易记录相关API ====================

    /**
     * 获取交易记录列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取我的交易记录",
            description = "分页查询当前会员的交易流水记录，支持按类型、时间范围等条件筛选"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetTransactionListApiDoc {
    }

    /**
     * 获取交易记录详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取交易记录详情",
            description = "根据交易记录ID获取详细信息"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "交易记录不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetTransactionDetailApiDoc {
    }

    // ==================== 使用记录相关API ====================

    /**
     * 获取使用记录列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取我的使用记录",
            description = "分页查询当前会员的模型使用记录，支持按模型、时间范围等条件筛选"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetUsageListApiDoc {
    }

    /**
     * 获取使用记录详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取使用记录详情",
            description = "根据使用记录ID获取详细信息"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "使用记录不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetUsageDetailApiDoc {
    }

    /**
     * 获取使用统计API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取我的使用统计",
            description = "获取当前会员的使用统计信息，包括本月、今日使用量等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetUsageStatsApiDoc {
    }
}
