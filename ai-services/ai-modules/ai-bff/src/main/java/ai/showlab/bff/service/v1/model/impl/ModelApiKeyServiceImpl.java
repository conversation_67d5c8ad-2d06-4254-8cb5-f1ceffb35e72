package ai.showlab.bff.service.v1.model.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.model.ModelApiKey;
import ai.showlab.bff.mapper.v1.model.ModelApiKeyMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.model.IModelApiKeyService;
import ai.showlab.common.core.constant.CacheConstants;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 模型API Key服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class ModelApiKeyServiceImpl extends BaseService implements IModelApiKeyService {

    private final AtomicInteger roundRobinCounter = new AtomicInteger(0);
    @Autowired
    private ModelApiKeyMapper modelApiKeyMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_MODEL_API_KEYS_KEY, key = "#providerId")
    public List<ModelApiKey> getApiKeysByProvider(Long providerId) {
        return modelApiKeyMapper.selectModelApiKeysByProviderId(providerId);
    }

    @Override
    public ModelApiKey getOneAvailableApiKey(Long providerId) {
        List<ModelApiKey> apiKeys = getApiKeysByProvider(providerId);
        if (apiKeys == null || apiKeys.isEmpty()) {
            throw new ServiceException("该供应商下无可用API Key");
        }

        // 简单的轮询策略
        int index = roundRobinCounter.getAndIncrement() % apiKeys.size();
        return apiKeys.get(index);
    }
} 