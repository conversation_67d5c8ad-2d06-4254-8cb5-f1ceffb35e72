package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 会员余额视图对象
 * <p>
 * 用于向前端展示会员的余额信息，包含关联的货币信息。
 * </p>
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingBalanceVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 可用余额
     */
    private BigDecimal balance;

    /**
     * 冻结金额（如用于待支付订单）
     */
    private BigDecimal frozenAmount;
    
    /**
     * 总余额（可用余额 + 冻结金额）
     */
    private BigDecimal totalBalance;

    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;
    
    /**
     * 货币代码 (如 CNY, USD)
     */
    private String currencyCode;

    /**
     * 余额预警阈值
     */
    private BigDecimal lowThreshold;
    
    /**
     * 是否低于预警阈值
     */
    private Boolean isLowBalance;

    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
}
