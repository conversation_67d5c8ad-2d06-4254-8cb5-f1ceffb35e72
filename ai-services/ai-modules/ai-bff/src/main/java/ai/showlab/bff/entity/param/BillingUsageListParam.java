package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 使用记录列表查询参数
 * <p>
 * 用于查询当前会员的模型使用记录，支持按模型、时间范围等条件筛选。
 * </p>
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class BillingUsageListParam {
    
    /**
     * 模型ID，可选
     */
    private Long modelId;
    
    /**
     * 计费单位，可选 (字典: billing_unit), 1-Token, 2-次, 3-张(图), 4-秒(音频)
     */
    private Integer unit;
    
    /**
     * 开始时间，可选
     */
    private OffsetDateTime startTime;
    
    /**
     * 结束时间，可选
     */
    private OffsetDateTime endTime;
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
