package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建订单参数
 * <p>
 * 用于用户购买套餐时创建订单。
 * </p>
 * 
 * <AUTHOR>
 */
@Data
public class BillingOrderCreateParam {
    
    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    private Long packageId;
    
    /**
     * 支付网关ID
     */
    @NotNull(message = "支付网关ID不能为空")
    private Long paymentGatewayId;
}
