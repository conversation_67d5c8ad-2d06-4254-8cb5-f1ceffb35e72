package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 使用记录视图对象
 * <p>
 * 用于向前端展示模型使用记录，包含关联的模型信息和计费信息。
 * </p>
 * 
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingUsageVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 计费方案ID
     */
    private Long planId;
    
    /**
     * 计费方案名称
     */
    private String planName;

    /**
     * 计费单位 (字典: billing_unit), 1-Token, 2-次, 3-张(图), 4-秒(音频)
     */
    private Integer unit;
    
    /**
     * 计费单位描述
     */
    private String unitDesc;

    /**
     * 本次使用量
     */
    private BigDecimal amount;

    /**
     * 请求耗时（毫秒）
     */
    private Integer durationMs;

    /**
     * 返回结果大小（字节）
     */
    private Integer resultSize;
    
    /**
     * 本次消费金额
     */
    private BigDecimal cost;
    
    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;

    /**
     * 使用时间
     */
    private OffsetDateTime usedTime;

    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
}
