package ai.showlab.bff.service.v1.model.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.model.ModelFeature;
import ai.showlab.bff.mapper.v1.model.ModelFeatureMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.model.IModelFeatureService;
import ai.showlab.common.core.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型特性服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class ModelFeatureServiceImpl extends BaseService implements IModelFeatureService {

    @Autowired
    private ModelFeatureMapper modelFeatureMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_MODEL_FEATURES_KEY, key = "#modelId")
    public List<ModelFeature> getFeaturesByModelId(Long modelId) {
        return modelFeatureMapper.selectModelFeaturesByModelId(modelId);
    }
} 