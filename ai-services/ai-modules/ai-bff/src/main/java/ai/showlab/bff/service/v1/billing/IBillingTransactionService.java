package ai.showlab.bff.service.v1.billing;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.param.BillingTransactionListParam;
import ai.showlab.bff.entity.vo.v1.BillingTransactionVo;
import ai.showlab.common.core.web.page.PageResult;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 计费交易记录服务接口
 * <p>
 * 定义了面向外部用户的交易记录相关业务逻辑。
 * 包括交易记录查询、统计分析等功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IBillingTransactionService {

    // ==================== 交易记录查询功能 ====================

    /**
     * 分页查询当前会员的交易记录
     * <p>
     * 支持按类型、时间范围等条件筛选，返回适合前端展示的VO对象。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 分页结果
     */
    PageResult<BillingTransactionVo> getMemberTransactionList(RequestParams<BillingTransactionListParam> requestParams);

    /**
     * 根据ID获取交易记录详情
     * <p>
     * 返回交易记录的详细信息。
     * 只能查询当前会员的交易记录。
     * </p>
     *
     * @param transactionId 交易记录ID
     * @return 交易记录详情VO
     */
    BillingTransactionVo getTransactionDetail(Long transactionId);

    // ==================== 交易统计功能 ====================

    /**
     * 获取当前会员的消费统计
     * <p>
     * 统计指定时间范围内的消费总额。
     * </p>
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 消费总额
     */
    BigDecimal getMemberConsumptionTotal(OffsetDateTime startTime, OffsetDateTime endTime);

    /**
     * 获取当前会员的充值统计
     * <p>
     * 统计指定时间范围内的充值总额。
     * </p>
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 充值总额
     */
    BigDecimal getMemberRechargeTotal(OffsetDateTime startTime, OffsetDateTime endTime);

    /**
     * 获取当前会员本月的消费统计
     * <p>
     * 统计当前月份的消费总额。
     * </p>
     *
     * @return 本月消费总额
     */
    BigDecimal getCurrentMonthConsumption();

    /**
     * 获取当前会员今日的消费统计
     * <p>
     * 统计今日的消费总额。
     * </p>
     *
     * @return 今日消费总额
     */
    BigDecimal getTodayConsumption();

    // ==================== 内部方法 ====================

    /**
     * 创建交易记录
     * <p>
     * 用于内部业务逻辑，记录各种类型的交易。
     * </p>
     *
     * @param memberId      会员ID
     * @param type          交易类型
     * @param amount        交易金额
     * @param currencyId    货币ID
     * @param referenceId   关联ID
     * @param description   描述
     * @return 是否创建成功
     */
    boolean createTransaction(Long memberId, Integer type, BigDecimal amount, 
                            Long currencyId, Long referenceId, String description);

    /**
     * 检查交易记录是否属于指定会员
     * <p>
     * 用于权限验证。
     * </p>
     *
     * @param transactionId 交易记录ID
     * @param memberId      会员ID
     * @return 是否属于该会员
     */
    boolean isTransactionBelongToMember(Long transactionId, Long memberId);
}
