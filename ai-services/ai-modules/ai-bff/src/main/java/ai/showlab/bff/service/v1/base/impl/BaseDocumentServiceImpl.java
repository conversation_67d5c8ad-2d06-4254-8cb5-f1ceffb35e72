package ai.showlab.bff.service.v1.base.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.base.BaseDocument;
import ai.showlab.bff.mapper.v1.base.BaseDocumentMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.base.IBaseDocumentService;
import ai.showlab.common.core.constant.CacheConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 文档表 服务实现层
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class BaseDocumentServiceImpl extends BaseService implements IBaseDocumentService {
    private static final Logger log = LoggerFactory.getLogger(BaseDocumentServiceImpl.class);

    @Autowired
    private BaseDocumentMapper documentMapper;

    @Override
    @CustomCache(value = CacheConstants.BFF_DOCUMENT_KEY, key = "#code + ':' + #langId", ttl = 36000)
    public BaseDocument getPublishedDocumentByCode(String code, Long langId) {
        return documentMapper.selectPublishedDocumentByCode(code, langId);
    }
} 