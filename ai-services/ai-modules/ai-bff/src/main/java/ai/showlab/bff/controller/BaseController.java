package ai.showlab.bff.controller;

import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.util.ResponseUtils;
import ai.showlab.common.core.constant.CodeConstants;
import ai.showlab.common.core.web.domain.RestResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;

/**
 * 控制器基类
 *
 * <AUTHOR>
 */
@Validated
public class BaseController {
    private static final Logger log = LoggerFactory.getLogger(BaseController.class);

    /**
     * 检查对象是否为空，为空则抛出业务异常
     *
     * @param obj 待检查对象
     * @param msg 异常信息
     * @param <T> 对象类型
     * @return 非空对象
     */
    protected <T> T checkNotNull(T obj, String msg) {
        if (obj == null) {
            throw new BusinessException(CodeConstants.NOT_FOUND, msg);
        }
        return obj;
    }

    /**
     * 执行业务操作并处理异常，返回统一格式的响应
     * <p>
     * 优化后的异常处理方法，主要处理BusinessException，
     * 同时保留对其他异常的兜底处理。
     * </p>
     *
     * @param operation 业务操作（函数式接口）
     * @param errorMsg  发生未知异常时的错误信息
     * @return 统一格式的响应
     */
    protected <T> ResponseEntity<RestResult> executeWithTryCatch(BusinessOperation<T> operation, String errorMsg) {
        try {
            T result = operation.execute();
            return ResponseUtils.ok(result);
        } catch (BusinessException e) {
            log.warn("业务异常: code={}, message={}", e.getCode(), e.getMessage());
            // 根据错误码选择适当的HTTP状态码
            int httpStatus = switch (e.getCode()) {
                case CodeConstants.NOT_FOUND -> CodeConstants.NOT_FOUND;
                case CodeConstants.UNAUTHORIZED -> CodeConstants.UNAUTHORIZED;
                case CodeConstants.FORBIDDEN -> CodeConstants.FORBIDDEN;
                case CodeConstants.SERVER_ERROR -> CodeConstants.SERVER_ERROR;
                default -> CodeConstants.ERROR;
            };
            return ResponseUtils.custom(httpStatus, e.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            // 处理运行时异常（如NullPointerException等）
            log.error("运行时异常: {}", errorMsg, e);
            return ResponseUtils.serverError(errorMsg);
        } catch (Exception e) {
            // 兜底处理其他异常（理论上不应该到达这里，因为BusinessOperation只抛出BusinessException）
            log.error("未知异常: {}", errorMsg, e);
            return ResponseUtils.serverError(errorMsg);
        }
    }

    /**
     * Service层业务操作函数式接口
     * <p>
     * 用于Service层内部的异常处理，可以抛出任何异常，
     * 由executeServiceOperation方法统一转换为BusinessException。
     * </p>
     *
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface ServiceOperation<T> {
        /**
         * 执行Service层业务操作
         *
         * @return 业务操作结果
         * @throws Exception 可能抛出的任何异常
         */
        T execute() throws Exception;
    }

    /**
     * 业务操作函数式接口
     * <p>
     * 优化异常处理，使用BusinessException替代Exception，
     * 提供更精确的异常类型和更好的异常处理体验。
     * </p>
     *
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface BusinessOperation<T> {
        /**
         * 执行业务操作
         *
         * @return 业务操作结果
         * @throws BusinessException 业务异常，包含具体的错误码和错误信息
         */
        T execute() throws BusinessException;
    }
} 