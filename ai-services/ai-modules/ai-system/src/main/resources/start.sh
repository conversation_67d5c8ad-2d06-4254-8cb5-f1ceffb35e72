#!/bin/sh

#参数设置：
jarName=ai-system.jar
mainClass=ai.showlab.system.AiSystemApp
#启动了服务之后是否自动显示日志:
isTailLog=true
#JVM参数:
jvmOpts=
#日志路径:
logPath=./nohup.out
programdir=.


#------------------------------

#接收外部参数
if [ $# = 2 ] ; then
  jvmOpts="$1"
  logPath="$2"
fi

# Kill already existing process
existPid=0
stopTimes=0
while(true)
do
	#pid=`ps aux|grep ${mainClass} |awk '{if($11!="grep"){print $2;}}'`
	pid=`ps aux|grep ${mainClass} |awk '{if($11!="grep"){print $2;}}'`
	if [ "$pid" = "" ]
	then
	  nowTime=$(date +"%Y-%m-%d %H:%M:%S")
		if [ $stopTimes -eq 0 ]
		then
			echo $nowTime" no pid alive!"
		else
			echo $nowTime" [Stop OK] kill pid: $existPid"
		fi
		break
	else
		existPid=$pid
		ps aux|grep ${mainClass} |grep -v "grep"|awk '{if($11!="grep"){print $2;}}' | xargs kill -9
		stopTimes=$(($stopTimes + 1))
	fi
done

# start open new process:

source /etc/profile
libs=$programdir'/lib/*'
#libs=./lib/*
classPaths=$CLASSPATH

#setting libs path
append(){
  classPaths=$classPaths":"$1
}
for file in $libs; do
  append $file
done
append ":"$jarName
export CLASSPATH=$classPaths:.:$programdir
export LANG='zh_CN.UTF-8'
#nohup java -Dfile.encoding=utf-8 -classpath $CLASSPATH  $mainClass $jvmOpts > /dev/null 2>&1 &
nohup java -Dfile.encoding=utf-8 -classpath $CLASSPATH  $mainClass $jvmOpts > $logPath 2>&1 &

#check pid
sleep 1
nowTime=$(date +"%Y-%m-%d %H:%M:%S")
newPid=0
newPid=`jps -l | grep $mainClass | awk '{print $1}'`
if [ "$newPid" -ne 0 ]; then
	echo $nowTime" [Start OK] current pid: $newPid"
	if [ "$isTailLog" = true ]; then
		tail -0f $logPath
	fi
else
	echo $nowTime" [Start Failed]"
fi

exit 0

