<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.AssistantCategoryMapper">
    
    <resultMap type="ai.showlab.system.domain.AssistantCategory" id="AssistantCategoryResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAssistantCategoryVo">
        select id, pid, type, name, description, sort_order, delete_time, create_by, update_by, create_time, update_time from a_assistant_category
    </sql>

    <select id="selectAssistantCategoryList" parameterType="ai.showlab.system.domain.AssistantCategory" resultMap="AssistantCategoryResult">
        <include refid="selectAssistantCategoryVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectAssistantCategoryById" parameterType="Long" resultMap="AssistantCategoryResult">
        <include refid="selectAssistantCategoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertAssistantCategory" parameterType="ai.showlab.system.domain.AssistantCategory">
        insert into a_assistant_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pid != null">pid,</if>
            <if test="type != null">type,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pid != null">#{pid},</if>
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAssistantCategory" parameterType="ai.showlab.system.domain.AssistantCategory">
        update a_assistant_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssistantCategoryById" parameterType="Long">
        delete from a_assistant_category where id = #{id}
    </delete>

    <delete id="deleteAssistantCategoryByIds" parameterType="String">
        delete from a_assistant_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>