<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.AssistantMapper">
    
    <resultMap type="ai.showlab.system.domain.Assistant" id="AssistantResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="ownerMemberId"    column="owner_member_id"    />
        <result property="revenueShareRate"    column="revenue_share_rate"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="billingPackageIds"    column="billing_package_ids"    />
        <result property="knowledgeIds"    column="knowledge_ids"    />
        <result property="description"    column="description"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="promptTemplate"    column="prompt_template"    />
        <result property="interactionMode"    column="interaction_mode"    />
        <result property="modelSuggestions"    column="model_suggestions"    />
        <result property="version"    column="version"    />
        <result property="status"    column="status"    />
        <result property="usageCount"    column="usage_count"    />
        <result property="isPublic"    column="is_public"    />
        <result property="isPreset"    column="is_preset"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAssistantVo">
        select id, category_id, owner_member_id, revenue_share_rate, code, name, billing_package_ids, knowledge_ids, description, icon_url, prompt_template, interaction_mode, model_suggestions, version, status, usage_count, is_public, is_preset, delete_time, create_by, update_by, create_time, update_time from a_assistant
    </sql>

    <select id="selectAssistantList" parameterType="ai.showlab.system.domain.Assistant" resultMap="AssistantResult">
        <include refid="selectAssistantVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="ownerMemberId != null "> and owner_member_id = #{ownerMemberId}</if>
            <if test="params.beginRevenueShareRate != null and params.beginRevenueShareRate != '' and params.endRevenueShareRate != null and params.endRevenueShareRate != ''">
                and revenue_share_rate between CAST(#{params.beginRevenueShareRate} as timestamptz)
                and CAST(#{params.endRevenueShareRate} as timestamptz)
            </if>
            <if test="code != null  and code != ''"> and code like concat('%', #{code}, '%')</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="billingPackageIds != null  and billingPackageIds != ''"> and billing_package_ids like concat('%', #{billingPackageIds}, '%')</if>
            <if test="knowledgeIds != null  and knowledgeIds != ''"> and knowledge_ids like concat('%', #{knowledgeIds}, '%')</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="interactionMode != null "> and interaction_mode = #{interactionMode}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="usageCount != null "> and usage_count = #{usageCount}</if>
            <if test="isPublic != null "> and is_public = #{isPublic}</if>
            <if test="isPreset != null "> and is_preset = #{isPreset}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectAssistantById" parameterType="Long" resultMap="AssistantResult">
        <include refid="selectAssistantVo"/>
        where id = #{id}
    </select>

    <insert id="insertAssistant" parameterType="ai.showlab.system.domain.Assistant">
        insert into a_assistant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="ownerMemberId != null">owner_member_id,</if>
            <if test="revenueShareRate != null">revenue_share_rate,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="billingPackageIds != null">billing_package_ids,</if>
            <if test="knowledgeIds != null">knowledge_ids,</if>
            <if test="description != null">description,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="promptTemplate != null">prompt_template,</if>
            <if test="interactionMode != null">interaction_mode,</if>
            <if test="modelSuggestions != null">model_suggestions,</if>
            <if test="version != null">version,</if>
            <if test="status != null">status,</if>
            <if test="usageCount != null">usage_count,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="isPreset != null">is_preset,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="ownerMemberId != null">#{ownerMemberId},</if>
            <if test="revenueShareRate != null">#{revenueShareRate},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="billingPackageIds != null">#{billingPackageIds},</if>
            <if test="knowledgeIds != null">#{knowledgeIds},</if>
            <if test="description != null">#{description},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="promptTemplate != null">#{promptTemplate},</if>
            <if test="interactionMode != null">#{interactionMode},</if>
            <if test="modelSuggestions != null">#{modelSuggestions},</if>
            <if test="version != null">#{version},</if>
            <if test="status != null">#{status},</if>
            <if test="usageCount != null">#{usageCount},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="isPreset != null">#{isPreset},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAssistant" parameterType="ai.showlab.system.domain.Assistant">
        update a_assistant
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="ownerMemberId != null">owner_member_id = #{ownerMemberId},</if>
            <if test="revenueShareRate != null">revenue_share_rate = #{revenueShareRate},</if>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="billingPackageIds != null">billing_package_ids = #{billingPackageIds},</if>
            <if test="knowledgeIds != null">knowledge_ids = #{knowledgeIds},</if>
            <if test="description != null">description = #{description},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="promptTemplate != null">prompt_template = #{promptTemplate},</if>
            <if test="interactionMode != null">interaction_mode = #{interactionMode},</if>
            <if test="modelSuggestions != null">model_suggestions = #{modelSuggestions},</if>
            <if test="version != null">version = #{version},</if>
            <if test="status != null">status = #{status},</if>
            <if test="usageCount != null">usage_count = #{usageCount},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="isPreset != null">is_preset = #{isPreset},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssistantById" parameterType="Long">
        delete from a_assistant where id = #{id}
    </delete>

    <delete id="deleteAssistantByIds" parameterType="String">
        delete from a_assistant where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>