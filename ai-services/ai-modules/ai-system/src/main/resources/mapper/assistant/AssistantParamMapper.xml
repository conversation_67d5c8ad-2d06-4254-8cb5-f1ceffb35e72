<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.AssistantParamMapper">
    
    <resultMap type="ai.showlab.system.domain.AssistantParam" id="AssistantParamResult">
        <result property="id"    column="id"    />
        <result property="assistantId"    column="assistant_id"    />
        <result property="key"    column="key"    />
        <result property="label"    column="label"    />
        <result property="paramType"    column="param_type"    />
        <result property="defaultValue"    column="default_value"    />
        <result property="options"    column="options"    />
        <result property="description"    column="description"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAssistantParamVo">
        select id, assistant_id, key, label, param_type, default_value, options, description, sort_order, delete_time, create_by, update_by, create_time, update_time from a_assistant_param
    </sql>

    <select id="selectAssistantParamList" parameterType="ai.showlab.system.domain.AssistantParam" resultMap="AssistantParamResult">
        <include refid="selectAssistantParamVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="assistantId != null "> and assistant_id = #{assistantId}</if>
            <if test="key != null  and key != ''"> and key like concat('%', #{key}, '%')</if>
            <if test="label != null  and label != ''"> and label like concat('%', #{label}, '%')</if>
            <if test="paramType != null "> and param_type = #{paramType}</if>
            <if test="options != null  and options != ''"> and options = #{options}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectAssistantParamById" parameterType="Long" resultMap="AssistantParamResult">
        <include refid="selectAssistantParamVo"/>
        where id = #{id}
    </select>

    <insert id="insertAssistantParam" parameterType="ai.showlab.system.domain.AssistantParam">
        insert into a_assistant_param
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assistantId != null">assistant_id,</if>
            <if test="key != null">key,</if>
            <if test="label != null">label,</if>
            <if test="paramType != null">param_type,</if>
            <if test="defaultValue != null">default_value,</if>
            <if test="options != null">options,</if>
            <if test="description != null">description,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assistantId != null">#{assistantId},</if>
            <if test="key != null">#{key},</if>
            <if test="label != null">#{label},</if>
            <if test="paramType != null">#{paramType},</if>
            <if test="defaultValue != null">#{defaultValue},</if>
            <if test="options != null">#{options},</if>
            <if test="description != null">#{description},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAssistantParam" parameterType="ai.showlab.system.domain.AssistantParam">
        update a_assistant_param
        <trim prefix="SET" suffixOverrides=",">
            <if test="assistantId != null">assistant_id = #{assistantId},</if>
            <if test="key != null">key = #{key},</if>
            <if test="label != null">label = #{label},</if>
            <if test="paramType != null">param_type = #{paramType},</if>
            <if test="defaultValue != null">default_value = #{defaultValue},</if>
            <if test="options != null">options = #{options},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssistantParamById" parameterType="Long">
        delete from a_assistant_param where id = #{id}
    </delete>

    <delete id="deleteAssistantParamByIds" parameterType="String">
        delete from a_assistant_param where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>