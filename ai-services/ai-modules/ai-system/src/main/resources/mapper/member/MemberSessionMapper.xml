<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.MemberSessionMapper">
    
    <resultMap type="ai.showlab.system.domain.MemberSession" id="MemberSessionResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="token"    column="token"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceInfo"    column="device_info"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="loginTime"    column="login_time"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="isActive"    column="is_active"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMemberSessionVo">
        select id, member_id, token, device_type, device_info, ip_address, login_time, expire_time, is_active, delete_time, create_time, update_time from a_member_session
    </sql>

    <select id="selectMemberSessionList" parameterType="ai.showlab.system.domain.MemberSession" resultMap="MemberSessionResult">
        <include refid="selectMemberSessionVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="token != null  and token != ''"> and token like concat('%', #{token}, '%')</if>
            <if test="deviceType != null "> and device_type = #{deviceType}</if>
            <if test="deviceInfo != null  and deviceInfo != ''"> and device_info like concat('%', #{deviceInfo}, '%')</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address like concat('%', #{ipAddress}, '%')</if>
            <if test="params.beginLoginTime != null and params.beginLoginTime != '' and params.endLoginTime != null and params.endLoginTime != ''">
                and login_time between CAST(#{params.beginLoginTime} as timestamptz)
                and CAST(#{params.endLoginTime} as timestamptz)
            </if>
            <if test="params.beginExpireTime != null and params.beginExpireTime != '' and params.endExpireTime != null and params.endExpireTime != ''">
                and expire_time between CAST(#{params.beginExpireTime} as timestamptz)
                and CAST(#{params.endExpireTime} as timestamptz)
            </if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectMemberSessionById" parameterType="Long" resultMap="MemberSessionResult">
        <include refid="selectMemberSessionVo"/>
        where id = #{id}
    </select>

    <insert id="insertMemberSession" parameterType="ai.showlab.system.domain.MemberSession">
        insert into a_member_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="token != null">token,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="deviceInfo != null">device_info,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="loginTime != null">login_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="isActive != null">is_active,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="token != null">#{token},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="deviceInfo != null">#{deviceInfo},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="loginTime != null">#{loginTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMemberSession" parameterType="ai.showlab.system.domain.MemberSession">
        update a_member_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="token != null">token = #{token},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceInfo != null">device_info = #{deviceInfo},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="loginTime != null">login_time = #{loginTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberSessionById" parameterType="Long">
        delete from a_member_session where id = #{id}
    </delete>

    <delete id="deleteMemberSessionByIds" parameterType="String">
        delete from a_member_session where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>