<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.MemberAuthMapper">
    
    <resultMap type="ai.showlab.system.domain.MemberAuth" id="MemberAuthResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="authType"    column="auth_type"    />
        <result property="identifier"    column="identifier"    />
        <result property="credential"    column="credential"    />
        <result property="isVerified"    column="is_verified"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMemberAuthVo">
        select id, member_id, auth_type, identifier, credential, is_verified, delete_time, create_time, update_time from a_member_auth
    </sql>

    <select id="selectMemberAuthList" parameterType="ai.showlab.system.domain.MemberAuth" resultMap="MemberAuthResult">
        <include refid="selectMemberAuthVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="authType != null "> and auth_type = #{authType}</if>
            <if test="identifier != null  and identifier != ''"> and identifier like concat('%', #{identifier}, '%')</if>
            <if test="credential != null  and credential != ''"> and credential like concat('%', #{credential}, '%')</if>
            <if test="isVerified != null "> and is_verified = #{isVerified}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectMemberAuthById" parameterType="Long" resultMap="MemberAuthResult">
        <include refid="selectMemberAuthVo"/>
        where id = #{id}
    </select>

    <insert id="insertMemberAuth" parameterType="ai.showlab.system.domain.MemberAuth">
        insert into a_member_auth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="authType != null">auth_type,</if>
            <if test="identifier != null">identifier,</if>
            <if test="credential != null">credential,</if>
            <if test="isVerified != null">is_verified,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="authType != null">#{authType},</if>
            <if test="identifier != null">#{identifier},</if>
            <if test="credential != null">#{credential},</if>
            <if test="isVerified != null">#{isVerified},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMemberAuth" parameterType="ai.showlab.system.domain.MemberAuth">
        update a_member_auth
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="authType != null">auth_type = #{authType},</if>
            <if test="identifier != null">identifier = #{identifier},</if>
            <if test="credential != null">credential = #{credential},</if>
            <if test="isVerified != null">is_verified = #{isVerified},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberAuthById" parameterType="Long">
        delete from a_member_auth where id = #{id}
    </delete>

    <delete id="deleteMemberAuthByIds" parameterType="String">
        delete from a_member_auth where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>