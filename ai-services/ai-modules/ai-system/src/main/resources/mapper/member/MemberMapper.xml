<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.MemberMapper">
    
    <resultMap type="ai.showlab.system.domain.Member" id="MemberResult">
        <result property="id"    column="id"    />
        <result property="memberType"    column="member_type"    />
        <result property="memberLevel"    column="member_level"    />
        <result property="username"    column="username"    />
        <result property="nickname"    column="nickname"    />
        <result property="password"    column="password"    />
        <result property="avatar"    column="avatar"    />
        <result property="gender"    column="gender"    />
        <result property="email"    column="email"    />
        <result property="phone"    column="phone"    />
        <result property="invitationCode"    column="invitation_code"    />
        <result property="inviterId"    column="inviter_id"    />
        <result property="status"    column="status"    />
        <result property="countryId"    column="country_id"    />
        <result property="langId"    column="lang_id"    />
        <result property="lastLoginIp"    column="last_login_ip"    />
        <result property="lastLoginTime"    column="last_login_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMemberVo">
        select id, member_type, member_level, username, nickname, password, avatar, gender, email, phone, invitation_code, inviter_id, status, country_id, lang_id, last_login_ip, last_login_time, delete_time, create_time, update_time from a_member
    </sql>

    <select id="selectMemberList" parameterType="ai.showlab.system.domain.Member" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberType != null "> and member_type = #{memberType}</if>
            <if test="memberLevel != null "> and member_level = #{memberLevel}</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar like concat('%', #{avatar}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="invitationCode != null  and invitationCode != ''"> and invitation_code like concat('%', #{invitationCode}, '%')</if>
            <if test="inviterId != null "> and inviter_id = #{inviterId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="countryId != null "> and country_id = #{countryId}</if>
            <if test="langId != null "> and lang_id = #{langId}</if>
            <if test="params.beginLastLoginTime != null and params.beginLastLoginTime != '' and params.endLastLoginTime != null and params.endLastLoginTime != ''">
                and last_login_time between CAST(#{params.beginLastLoginTime} as timestamptz)
                and CAST(#{params.endLastLoginTime} as timestamptz)
            </if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectMemberById" parameterType="Long" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        where id = #{id}
    </select>

    <insert id="insertMember" parameterType="ai.showlab.system.domain.Member">
        insert into a_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberType != null">member_type,</if>
            <if test="memberLevel != null">member_level,</if>
            <if test="username != null">username,</if>
            <if test="nickname != null">nickname,</if>
            <if test="password != null">password,</if>
            <if test="avatar != null">avatar,</if>
            <if test="gender != null">gender,</if>
            <if test="email != null">email,</if>
            <if test="phone != null">phone,</if>
            <if test="invitationCode != null">invitation_code,</if>
            <if test="inviterId != null">inviter_id,</if>
            <if test="status != null">status,</if>
            <if test="countryId != null">country_id,</if>
            <if test="langId != null">lang_id,</if>
            <if test="lastLoginIp != null">last_login_ip,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberType != null">#{memberType},</if>
            <if test="memberLevel != null">#{memberLevel},</if>
            <if test="username != null">#{username},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="password != null">#{password},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="gender != null">#{gender},</if>
            <if test="email != null">#{email},</if>
            <if test="phone != null">#{phone},</if>
            <if test="invitationCode != null">#{invitationCode},</if>
            <if test="inviterId != null">#{inviterId},</if>
            <if test="status != null">#{status},</if>
            <if test="countryId != null">#{countryId},</if>
            <if test="langId != null">#{langId},</if>
            <if test="lastLoginIp != null">#{lastLoginIp},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMember" parameterType="ai.showlab.system.domain.Member">
        update a_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="memberLevel != null">member_level = #{memberLevel},</if>
            <if test="username != null">username = #{username},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="password != null">password = #{password},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="invitationCode != null">invitation_code = #{invitationCode},</if>
            <if test="inviterId != null">inviter_id = #{inviterId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="countryId != null">country_id = #{countryId},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            <if test="lastLoginIp != null">last_login_ip = #{lastLoginIp},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberById" parameterType="Long">
        delete from a_member where id = #{id}
    </delete>

    <delete id="deleteMemberByIds" parameterType="String">
        delete from a_member where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>