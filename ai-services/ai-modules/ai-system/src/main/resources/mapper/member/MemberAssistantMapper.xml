<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.MemberAssistantMapper">
    
    <resultMap type="ai.showlab.system.domain.MemberAssistant" id="MemberAssistantResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="assistantId"    column="assistant_id"    />
        <result property="templateVersion"    column="template_version"    />
        <result property="modelId"    column="model_id"    />
        <result property="customName"    column="custom_name"    />
        <result property="settingsOverride"    column="settings_override"    />
        <result property="isFavorite"    column="is_favorite"    />
        <result property="isActive"    column="is_active"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMemberAssistantVo">
        select id, member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, delete_time, create_time, update_time from a_member_assistant
    </sql>

    <select id="selectMemberAssistantList" parameterType="ai.showlab.system.domain.MemberAssistant" resultMap="MemberAssistantResult">
        <include refid="selectMemberAssistantVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="assistantId != null "> and assistant_id = #{assistantId}</if>
            <if test="templateVersion != null  and templateVersion != ''"> and template_version like concat('%', #{templateVersion}, '%')</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="customName != null  and customName != ''"> and custom_name like concat('%', #{customName}, '%')</if>
            <if test="settingsOverride != null  and settingsOverride != ''"> and settings_override like concat('%', #{settingsOverride}, '%')</if>
            <if test="isFavorite != null "> and is_favorite = #{isFavorite}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectMemberAssistantById" parameterType="Long" resultMap="MemberAssistantResult">
        <include refid="selectMemberAssistantVo"/>
        where id = #{id}
    </select>

    <insert id="insertMemberAssistant" parameterType="ai.showlab.system.domain.MemberAssistant">
        insert into a_member_assistant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="assistantId != null">assistant_id,</if>
            <if test="templateVersion != null">template_version,</if>
            <if test="modelId != null">model_id,</if>
            <if test="customName != null">custom_name,</if>
            <if test="settingsOverride != null">settings_override,</if>
            <if test="isFavorite != null">is_favorite,</if>
            <if test="isActive != null">is_active,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="assistantId != null">#{assistantId},</if>
            <if test="templateVersion != null">#{templateVersion},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="customName != null">#{customName},</if>
            <if test="settingsOverride != null">#{settingsOverride},</if>
            <if test="isFavorite != null">#{isFavorite},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMemberAssistant" parameterType="ai.showlab.system.domain.MemberAssistant">
        update a_member_assistant
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="assistantId != null">assistant_id = #{assistantId},</if>
            <if test="templateVersion != null">template_version = #{templateVersion},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="customName != null">custom_name = #{customName},</if>
            <if test="settingsOverride != null">settings_override = #{settingsOverride},</if>
            <if test="isFavorite != null">is_favorite = #{isFavorite},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberAssistantById" parameterType="Long">
        delete from a_member_assistant where id = #{id}
    </delete>

    <delete id="deleteMemberAssistantByIds" parameterType="String">
        delete from a_member_assistant where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>