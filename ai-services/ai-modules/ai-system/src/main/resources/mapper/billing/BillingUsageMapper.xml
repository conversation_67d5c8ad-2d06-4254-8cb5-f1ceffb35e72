<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.BillingUsageMapper">
    
    <resultMap type="ai.showlab.system.domain.BillingUsage" id="BillingUsageResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="modelId"    column="model_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="unit"    column="unit"    />
        <result property="amount"    column="amount"    />
        <result property="durationMs"    column="duration_ms"    />
        <result property="resultSize"    column="result_size"    />
        <result property="usedTime"    column="used_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBillingUsageVo">
        select id, member_id, model_id, plan_id, unit, amount, duration_ms, result_size, used_time, create_time from a_billing_usage
    </sql>

    <select id="selectBillingUsageList" parameterType="ai.showlab.system.domain.BillingUsage" resultMap="BillingUsageResult">
        <include refid="selectBillingUsageVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="unit != null "> and unit = #{unit}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="durationMs != null "> and duration_ms = #{durationMs}</if>
            <if test="resultSize != null "> and result_size = #{resultSize}</if>
            <if test="params.beginUsedTime != null and params.beginUsedTime != '' and params.endUsedTime != null and params.endUsedTime != ''">
                and used_time between CAST(#{params.beginUsedTime} as timestamptz)
                and CAST(#{params.endUsedTime} as timestamptz)
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by create_time desc
    </select>
    
    <select id="selectBillingUsageById" parameterType="Long" resultMap="BillingUsageResult">
        <include refid="selectBillingUsageVo"/>
        where id = #{id}
    </select>

    <insert id="insertBillingUsage" parameterType="ai.showlab.system.domain.BillingUsage">
        insert into a_billing_usage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="unit != null">unit,</if>
            <if test="amount != null">amount,</if>
            <if test="durationMs != null">duration_ms,</if>
            <if test="resultSize != null">result_size,</if>
            <if test="usedTime != null">used_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="unit != null">#{unit},</if>
            <if test="amount != null">#{amount},</if>
            <if test="durationMs != null">#{durationMs},</if>
            <if test="resultSize != null">#{resultSize},</if>
            <if test="usedTime != null">#{usedTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBillingUsage" parameterType="ai.showlab.system.domain.BillingUsage">
        update a_billing_usage
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="durationMs != null">duration_ms = #{durationMs},</if>
            <if test="resultSize != null">result_size = #{resultSize},</if>
            <if test="usedTime != null">used_time = #{usedTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillingUsageById" parameterType="Long">
        delete from a_billing_usage where id = #{id}
    </delete>

    <delete id="deleteBillingUsageByIds" parameterType="String">
        delete from a_billing_usage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>