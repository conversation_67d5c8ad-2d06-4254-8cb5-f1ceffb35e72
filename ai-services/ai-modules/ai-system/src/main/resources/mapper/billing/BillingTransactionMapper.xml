<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.BillingTransactionMapper">
    
    <resultMap type="ai.showlab.system.domain.BillingTransaction" id="BillingTransactionResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="type"    column="type"    />
        <result property="amount"    column="amount"    />
        <result property="currencyId"    column="currency_id"    />
        <result property="referenceId"    column="reference_id"    />
        <result property="description"    column="description"    />
        <result property="transactionTime"    column="transaction_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillingTransactionVo">
        select id, member_id, type, amount, currency_id, reference_id, description, transaction_time, create_time, update_time from a_billing_transaction
    </sql>

    <select id="selectBillingTransactionList" parameterType="ai.showlab.system.domain.BillingTransaction" resultMap="BillingTransactionResult">
        <include refid="selectBillingTransactionVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="currencyId != null "> and currency_id = #{currencyId}</if>
            <if test="referenceId != null "> and reference_id = #{referenceId}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="params.beginTransactionTime != null and params.beginTransactionTime != '' and params.endTransactionTime != null and params.endTransactionTime != ''">
                and transaction_time between CAST(#{params.beginTransactionTime} as timestamptz)
                and CAST(#{params.endTransactionTime} as timestamptz)
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectBillingTransactionById" parameterType="Long" resultMap="BillingTransactionResult">
        <include refid="selectBillingTransactionVo"/>
        where id = #{id}
    </select>

    <insert id="insertBillingTransaction" parameterType="ai.showlab.system.domain.BillingTransaction">
        insert into a_billing_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="type != null">type,</if>
            <if test="amount != null">amount,</if>
            <if test="currencyId != null">currency_id,</if>
            <if test="referenceId != null">reference_id,</if>
            <if test="description != null">description,</if>
            <if test="transactionTime != null">transaction_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="type != null">#{type},</if>
            <if test="amount != null">#{amount},</if>
            <if test="currencyId != null">#{currencyId},</if>
            <if test="referenceId != null">#{referenceId},</if>
            <if test="description != null">#{description},</if>
            <if test="transactionTime != null">#{transactionTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBillingTransaction" parameterType="ai.showlab.system.domain.BillingTransaction">
        update a_billing_transaction
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="currencyId != null">currency_id = #{currencyId},</if>
            <if test="referenceId != null">reference_id = #{referenceId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="transactionTime != null">transaction_time = #{transactionTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillingTransactionById" parameterType="Long">
        delete from a_billing_transaction where id = #{id}
    </delete>

    <delete id="deleteBillingTransactionByIds" parameterType="String">
        delete from a_billing_transaction where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>