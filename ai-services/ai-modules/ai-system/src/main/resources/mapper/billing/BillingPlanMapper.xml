<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.BillingPlanMapper">
    
    <resultMap type="ai.showlab.system.domain.BillingPlan" id="BillingPlanResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="unit"    column="unit"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillingPlanVo">
        select id, name, unit, description, status, create_by, update_by, create_time, update_time from a_billing_plan
    </sql>

    <select id="selectBillingPlanList" parameterType="ai.showlab.system.domain.BillingPlan" resultMap="BillingPlanResult">
        <include refid="selectBillingPlanVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="unit != null "> and unit = #{unit}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectBillingPlanById" parameterType="Long" resultMap="BillingPlanResult">
        <include refid="selectBillingPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertBillingPlan" parameterType="ai.showlab.system.domain.BillingPlan">
        insert into a_billing_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="unit != null">unit,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="unit != null">#{unit},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBillingPlan" parameterType="ai.showlab.system.domain.BillingPlan">
        update a_billing_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillingPlanById" parameterType="Long">
        delete from a_billing_plan where id = #{id}
    </delete>

    <delete id="deleteBillingPlanByIds" parameterType="String">
        delete from a_billing_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>