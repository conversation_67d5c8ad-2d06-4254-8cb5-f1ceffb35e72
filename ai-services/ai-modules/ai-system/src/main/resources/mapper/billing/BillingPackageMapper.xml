<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.BillingPackageMapper">
    
    <resultMap type="ai.showlab.system.domain.BillingPackage" id="BillingPackageResult">
        <result property="id"    column="id"    />
        <result property="ownerMemberId"    column="owner_member_id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="type"    column="type"    />
        <result property="price"    column="price"    />
        <result property="currencyId"    column="currency_id"    />
        <result property="creditsGranted"    column="credits_granted"    />
        <result property="validityDays"    column="validity_days"    />
        <result property="renewalInterval"    column="renewal_interval"    />
        <result property="renewalIntervalUnit"    column="renewal_interval_unit"    />
        <result property="memberLevelGrant"    column="member_level_grant"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillingPackageVo">
        select id, owner_member_id, code, name, description, type, price, currency_id, credits_granted, validity_days, renewal_interval, renewal_interval_unit, member_level_grant, status, sort_order, delete_time, create_by, update_by, create_time, update_time from a_billing_package
    </sql>

    <select id="selectBillingPackageList" parameterType="ai.showlab.system.domain.BillingPackage" resultMap="BillingPackageResult">
        <include refid="selectBillingPackageVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="ownerMemberId != null "> and owner_member_id = #{ownerMemberId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="currencyId != null "> and currency_id = #{currencyId}</if>
            <if test="creditsGranted != null "> and credits_granted = #{creditsGranted}</if>
            <if test="validityDays != null "> and validity_days = #{validityDays}</if>
            <if test="renewalInterval != null  and renewalInterval != ''"> and renewal_interval = #{renewalInterval}</if>
            <if test="renewalIntervalUnit != null "> and renewal_interval_unit = #{renewalIntervalUnit}</if>
            <if test="memberLevelGrant != null "> and member_level_grant = #{memberLevelGrant}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectBillingPackageById" parameterType="Long" resultMap="BillingPackageResult">
        <include refid="selectBillingPackageVo"/>
        where id = #{id}
    </select>

    <insert id="insertBillingPackage" parameterType="ai.showlab.system.domain.BillingPackage">
        insert into a_billing_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ownerMemberId != null">owner_member_id,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="type != null">type,</if>
            <if test="price != null">price,</if>
            <if test="currencyId != null">currency_id,</if>
            <if test="creditsGranted != null">credits_granted,</if>
            <if test="validityDays != null">validity_days,</if>
            <if test="renewalInterval != null">renewal_interval,</if>
            <if test="renewalIntervalUnit != null">renewal_interval_unit,</if>
            <if test="memberLevelGrant != null">member_level_grant,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ownerMemberId != null">#{ownerMemberId},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="type != null">#{type},</if>
            <if test="price != null">#{price},</if>
            <if test="currencyId != null">#{currencyId},</if>
            <if test="creditsGranted != null">#{creditsGranted},</if>
            <if test="validityDays != null">#{validityDays},</if>
            <if test="renewalInterval != null">#{renewalInterval},</if>
            <if test="renewalIntervalUnit != null">#{renewalIntervalUnit},</if>
            <if test="memberLevelGrant != null">#{memberLevelGrant},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBillingPackage" parameterType="ai.showlab.system.domain.BillingPackage">
        update a_billing_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="ownerMemberId != null">owner_member_id = #{ownerMemberId},</if>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
            <if test="price != null">price = #{price},</if>
            <if test="currencyId != null">currency_id = #{currencyId},</if>
            <if test="creditsGranted != null">credits_granted = #{creditsGranted},</if>
            <if test="validityDays != null">validity_days = #{validityDays},</if>
            <if test="renewalInterval != null">renewal_interval = #{renewalInterval},</if>
            <if test="renewalIntervalUnit != null">renewal_interval_unit = #{renewalIntervalUnit},</if>
            <if test="memberLevelGrant != null">member_level_grant = #{memberLevelGrant},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillingPackageById" parameterType="Long">
        delete from a_billing_package where id = #{id}
    </delete>

    <delete id="deleteBillingPackageByIds" parameterType="String">
        delete from a_billing_package where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>