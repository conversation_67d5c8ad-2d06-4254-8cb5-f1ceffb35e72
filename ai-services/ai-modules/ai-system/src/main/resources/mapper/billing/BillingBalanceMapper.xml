<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.BillingBalanceMapper">
    
    <resultMap type="ai.showlab.system.domain.BillingBalance" id="BillingBalanceResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="balance"    column="balance"    />
        <result property="frozenAmount"    column="frozen_amount"    />
        <result property="currencyId"    column="currency_id"    />
        <result property="lowThreshold"    column="low_threshold"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillingBalanceVo">
        select id, member_id, balance, frozen_amount, currency_id, low_threshold, create_time, update_time from a_billing_balance
    </sql>

    <select id="selectBillingBalanceList" parameterType="ai.showlab.system.domain.BillingBalance" resultMap="BillingBalanceResult">
        <include refid="selectBillingBalanceVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="frozenAmount != null "> and frozen_amount = #{frozenAmount}</if>
            <if test="currencyId != null "> and currency_id = #{currencyId}</if>
            <if test="lowThreshold != null "> and low_threshold = #{lowThreshold}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectBillingBalanceById" parameterType="Long" resultMap="BillingBalanceResult">
        <include refid="selectBillingBalanceVo"/>
        where id = #{id}
    </select>

    <insert id="insertBillingBalance" parameterType="ai.showlab.system.domain.BillingBalance">
        insert into a_billing_balance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="balance != null">balance,</if>
            <if test="frozenAmount != null">frozen_amount,</if>
            <if test="currencyId != null">currency_id,</if>
            <if test="lowThreshold != null">low_threshold,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="balance != null">#{balance},</if>
            <if test="frozenAmount != null">#{frozenAmount},</if>
            <if test="currencyId != null">#{currencyId},</if>
            <if test="lowThreshold != null">#{lowThreshold},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBillingBalance" parameterType="ai.showlab.system.domain.BillingBalance">
        update a_billing_balance
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="frozenAmount != null">frozen_amount = #{frozenAmount},</if>
            <if test="currencyId != null">currency_id = #{currencyId},</if>
            <if test="lowThreshold != null">low_threshold = #{lowThreshold},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillingBalanceById" parameterType="Long">
        delete from a_billing_balance where id = #{id}
    </delete>

    <delete id="deleteBillingBalanceByIds" parameterType="String">
        delete from a_billing_balance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>