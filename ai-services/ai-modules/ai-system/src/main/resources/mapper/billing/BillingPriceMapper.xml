<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.BillingPriceMapper">
    
    <resultMap type="ai.showlab.system.domain.BillingPrice" id="BillingPriceResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="memberLevel"    column="member_level"    />
        <result property="price"    column="price"    />
        <result property="currencyId"    column="currency_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillingPriceVo">
        select id, model_id, plan_id, member_level, price, currency_id, status, create_by, update_by, create_time, update_time from a_billing_price
    </sql>

    <select id="selectBillingPriceList" parameterType="ai.showlab.system.domain.BillingPrice" resultMap="BillingPriceResult">
        <include refid="selectBillingPriceVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="memberLevel != null "> and member_level = #{memberLevel}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="currencyId != null "> and currency_id = #{currencyId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectBillingPriceById" parameterType="Long" resultMap="BillingPriceResult">
        <include refid="selectBillingPriceVo"/>
        where id = #{id}
    </select>

    <insert id="insertBillingPrice" parameterType="ai.showlab.system.domain.BillingPrice">
        insert into a_billing_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="memberLevel != null">member_level,</if>
            <if test="price != null">price,</if>
            <if test="currencyId != null">currency_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="memberLevel != null">#{memberLevel},</if>
            <if test="price != null">#{price},</if>
            <if test="currencyId != null">#{currencyId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBillingPrice" parameterType="ai.showlab.system.domain.BillingPrice">
        update a_billing_price
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="memberLevel != null">member_level = #{memberLevel},</if>
            <if test="price != null">price = #{price},</if>
            <if test="currencyId != null">currency_id = #{currencyId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillingPriceById" parameterType="Long">
        delete from a_billing_price where id = #{id}
    </delete>

    <delete id="deleteBillingPriceByIds" parameterType="String">
        delete from a_billing_price where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>