<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.BillingOrderMapper">
    
    <resultMap type="ai.showlab.system.domain.BillingOrder" id="BillingOrderResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="memberId"    column="member_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="packageInfoSnapshot"    column="package_info_snapshot"    />
        <result property="amount"    column="amount"    />
        <result property="currencyId"    column="currency_id"    />
        <result property="status"    column="status"    />
        <result property="paymentGatewayId"    column="payment_gateway_id"    />
        <result property="gatewayTransactionId"    column="gateway_transaction_id"    />
        <result property="paidTime"    column="paid_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillingOrderVo">
        select id, order_no, member_id, package_id, package_info_snapshot, amount, currency_id, status, payment_gateway_id, gateway_transaction_id, paid_time, create_time, update_time from a_billing_order
    </sql>

    <select id="selectBillingOrderList" parameterType="ai.showlab.system.domain.BillingOrder" resultMap="BillingOrderResult">
        <include refid="selectBillingOrderVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="packageId != null "> and package_id = #{packageId}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="currencyId != null "> and currency_id = #{currencyId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="paymentGatewayId != null "> and payment_gateway_id = #{paymentGatewayId}</if>
            <if test="gatewayTransactionId != null  and gatewayTransactionId != ''"> and gateway_transaction_id like concat('%', #{gatewayTransactionId}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectBillingOrderById" parameterType="Long" resultMap="BillingOrderResult">
        <include refid="selectBillingOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertBillingOrder" parameterType="ai.showlab.system.domain.BillingOrder">
        insert into a_billing_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="memberId != null">member_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="packageInfoSnapshot != null">package_info_snapshot,</if>
            <if test="amount != null">amount,</if>
            <if test="currencyId != null">currency_id,</if>
            <if test="status != null">status,</if>
            <if test="paymentGatewayId != null">payment_gateway_id,</if>
            <if test="gatewayTransactionId != null">gateway_transaction_id,</if>
            <if test="paidTime != null">paid_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="packageInfoSnapshot != null">#{packageInfoSnapshot},</if>
            <if test="amount != null">#{amount},</if>
            <if test="currencyId != null">#{currencyId},</if>
            <if test="status != null">#{status},</if>
            <if test="paymentGatewayId != null">#{paymentGatewayId},</if>
            <if test="gatewayTransactionId != null">#{gatewayTransactionId},</if>
            <if test="paidTime != null">#{paidTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBillingOrder" parameterType="ai.showlab.system.domain.BillingOrder">
        update a_billing_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="packageInfoSnapshot != null">package_info_snapshot = #{packageInfoSnapshot},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="currencyId != null">currency_id = #{currencyId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="paymentGatewayId != null">payment_gateway_id = #{paymentGatewayId},</if>
            <if test="gatewayTransactionId != null">gateway_transaction_id = #{gatewayTransactionId},</if>
            <if test="paidTime != null">paid_time = #{paidTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillingOrderById" parameterType="Long">
        delete from a_billing_order where id = #{id}
    </delete>

    <delete id="deleteBillingOrderByIds" parameterType="String">
        delete from a_billing_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>