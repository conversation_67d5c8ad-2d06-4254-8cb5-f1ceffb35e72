<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.KnowledgeMapper">
    
    <resultMap type="ai.showlab.system.domain.Knowledge" id="KnowledgeResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="embeddingModelId"    column="embedding_model_id"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeVo">
        select id, member_id, name, description, icon_url, embedding_model_id, permission_type, delete_time, create_time, update_time from a_knowledge
    </sql>

    <select id="selectKnowledgeList" parameterType="ai.showlab.system.domain.Knowledge" resultMap="KnowledgeResult">
        <include refid="selectKnowledgeVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="iconUrl != null  and iconUrl != ''"> and icon_url like concat('%', #{iconUrl}, '%')</if>
            <if test="embeddingModelId != null "> and embedding_model_id = #{embeddingModelId}</if>
            <if test="permissionType != null "> and permission_type = #{permissionType}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectKnowledgeById" parameterType="Long" resultMap="KnowledgeResult">
        <include refid="selectKnowledgeVo"/>
        where id = #{id}
    </select>

    <insert id="insertKnowledge" parameterType="ai.showlab.system.domain.Knowledge">
        insert into a_knowledge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="embeddingModelId != null">embedding_model_id,</if>
            <if test="permissionType != null">permission_type,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="embeddingModelId != null">#{embeddingModelId},</if>
            <if test="permissionType != null">#{permissionType},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledge" parameterType="ai.showlab.system.domain.Knowledge">
        update a_knowledge
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="embeddingModelId != null">embedding_model_id = #{embeddingModelId},</if>
            <if test="permissionType != null">permission_type = #{permissionType},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeById" parameterType="Long">
        delete from a_knowledge where id = #{id}
    </delete>

    <delete id="deleteKnowledgeByIds" parameterType="String">
        delete from a_knowledge where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>