<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.KnowledgeDocChunkMapper">
    
    <resultMap type="ai.showlab.system.domain.KnowledgeDocChunk" id="KnowledgeDocChunkResult">
        <result property="id"    column="id"    />
        <result property="docId"    column="doc_id"    />
        <result property="content"    column="content"    />
        <result property="charCount"    column="char_count"    />
        <result property="metadata"    column="metadata"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeDocChunkVo">
        select id, doc_id, content, char_count, metadata, delete_time, create_time, update_time from a_knowledge_doc_chunk
    </sql>

    <select id="selectKnowledgeDocChunkList" parameterType="ai.showlab.system.domain.KnowledgeDocChunk" resultMap="KnowledgeDocChunkResult">
        <include refid="selectKnowledgeDocChunkVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="docId != null "> and doc_id = #{docId}</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="metadata != null  and metadata != ''"> and metadata like concat('%', #{metadata}, '%')</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectKnowledgeDocChunkById" parameterType="Long" resultMap="KnowledgeDocChunkResult">
        <include refid="selectKnowledgeDocChunkVo"/>
        where id = #{id}
    </select>

    <insert id="insertKnowledgeDocChunk" parameterType="ai.showlab.system.domain.KnowledgeDocChunk">
        insert into a_knowledge_doc_chunk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="docId != null">doc_id,</if>
            <if test="content != null">content,</if>
            <if test="charCount != null">char_count,</if>
            <if test="metadata != null">metadata,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="docId != null">#{docId},</if>
            <if test="content != null">#{content},</if>
            <if test="charCount != null">#{charCount},</if>
            <if test="metadata != null">#{metadata},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeDocChunk" parameterType="ai.showlab.system.domain.KnowledgeDocChunk">
        update a_knowledge_doc_chunk
        <trim prefix="SET" suffixOverrides=",">
            <if test="docId != null">doc_id = #{docId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="charCount != null">char_count = #{charCount},</if>
            <if test="metadata != null">metadata = #{metadata},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeDocChunkById" parameterType="Long">
        delete from a_knowledge_doc_chunk where id = #{id}
    </delete>

    <delete id="deleteKnowledgeDocChunkByIds" parameterType="String">
        delete from a_knowledge_doc_chunk where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>