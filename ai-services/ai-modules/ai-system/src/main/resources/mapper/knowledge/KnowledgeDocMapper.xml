<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.KnowledgeDocMapper">
    
    <resultMap type="ai.showlab.system.domain.KnowledgeDoc" id="KnowledgeDocResult">
        <result property="id"    column="id"    />
        <result property="knowledgeId"    column="knowledge_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSize"    column="file_size"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="processingStatus"    column="processing_status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="chunkCount"    column="chunk_count"    />
        <result property="charCount"    column="char_count"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="KnowledgeDocKnowledgeDocChunkResult" type="ai.showlab.system.domain.KnowledgeDoc" extends="KnowledgeDocResult">
        <collection property="knowledgeDocChunkList" ofType="KnowledgeDocChunk" column="id" select="selectKnowledgeDocChunkList" />
    </resultMap>

    <resultMap type="KnowledgeDocChunk" id="KnowledgeDocChunkResult">
        <result property="id"    column="id"    />
        <result property="docId"    column="doc_id"    />
        <result property="content"    column="content"    />
        <result property="charCount"    column="char_count"    />
        <result property="metadata"    column="metadata"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeDocVo">
        select id, knowledge_id, file_name, file_type, file_size, storage_path, processing_status, error_message, chunk_count, char_count, delete_time, create_time, update_time from a_knowledge_doc
    </sql>

    <select id="selectKnowledgeDocList" parameterType="ai.showlab.system.domain.KnowledgeDoc" resultMap="KnowledgeDocResult">
        <include refid="selectKnowledgeDocVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="knowledgeId != null "> and knowledge_id = #{knowledgeId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="storagePath != null  and storagePath != ''"> and storage_path like concat('%', #{storagePath}, '%')</if>
            <if test="processingStatus != null "> and processing_status = #{processingStatus}</if>
            <if test="errorMessage != null  and errorMessage != ''"> and error_message like concat('%', #{errorMessage}, '%')</if>
            <if test="chunkCount != null "> and chunk_count = #{chunkCount}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectKnowledgeDocById" parameterType="Long" resultMap="KnowledgeDocKnowledgeDocChunkResult">
        select id, knowledge_id, file_name, file_type, file_size, storage_path, processing_status, error_message, chunk_count, char_count, delete_time, create_time, update_time
        from a_knowledge_doc
        where id = #{id}
    </select>

    <select id="selectKnowledgeDocChunkList" resultMap="KnowledgeDocChunkResult">
        select id, doc_id, content, char_count, metadata, delete_time, create_time, update_time
        from a_knowledge_doc_chunk
        where doc_id = #{doc_id}
    </select>

    <insert id="insertKnowledgeDoc" parameterType="ai.showlab.system.domain.KnowledgeDoc">
        insert into a_knowledge_doc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="knowledgeId != null">knowledge_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="storagePath != null">storage_path,</if>
            <if test="processingStatus != null">processing_status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="chunkCount != null">chunk_count,</if>
            <if test="charCount != null">char_count,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="knowledgeId != null">#{knowledgeId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="storagePath != null">#{storagePath},</if>
            <if test="processingStatus != null">#{processingStatus},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="chunkCount != null">#{chunkCount},</if>
            <if test="charCount != null">#{charCount},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeDoc" parameterType="ai.showlab.system.domain.KnowledgeDoc">
        update a_knowledge_doc
        <trim prefix="SET" suffixOverrides=",">
            <if test="knowledgeId != null">knowledge_id = #{knowledgeId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="storagePath != null">storage_path = #{storagePath},</if>
            <if test="processingStatus != null">processing_status = #{processingStatus},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="chunkCount != null">chunk_count = #{chunkCount},</if>
            <if test="charCount != null">char_count = #{charCount},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeDocById" parameterType="Long">
        delete from a_knowledge_doc where id = #{id}
    </delete>

    <delete id="deleteKnowledgeDocByIds" parameterType="String">
        delete from a_knowledge_doc where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteKnowledgeDocChunkByDocIds" parameterType="String">
        delete from a_knowledge_doc_chunk where doc_id in 
        <foreach item="docId" collection="array" open="(" separator="," close=")">
            #{docId}
        </foreach>
    </delete>

    <delete id="deleteKnowledgeDocChunkByDocId" parameterType="Long">
        delete from a_knowledge_doc_chunk where doc_id = #{docId}
    </delete>

    <insert id="batchKnowledgeDocChunk">
        insert into a_knowledge_doc_chunk( id, doc_id, content, char_count, metadata, delete_time, create_time, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.docId}, #{item.content}, #{item.charCount}, #{item.metadata}, #{item.deleteTime}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>