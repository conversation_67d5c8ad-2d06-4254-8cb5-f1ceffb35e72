<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.ModelProviderMapper">
    
    <resultMap type="ai.showlab.system.domain.ModelProvider" id="ModelProviderResult">
        <result property="id"    column="id"    />
        <result property="providerName"    column="provider_name"    />
        <result property="providerKey"    column="provider_key"    />
        <result property="channelType"    column="channel_type"    />
        <result property="baseUrl"    column="base_url"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectModelProviderVo">
        select id, provider_name, provider_key, channel_type, base_url, description, create_by, update_by, create_time, update_time from a_model_provider
    </sql>

    <select id="selectModelProviderList" parameterType="ai.showlab.system.domain.ModelProvider" resultMap="ModelProviderResult">
        <include refid="selectModelProviderVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="providerName != null  and providerName != ''"> and provider_name like concat('%', #{providerName}, '%')</if>
            <if test="providerKey != null  and providerKey != ''"> and provider_key like concat('%', #{providerKey}, '%')</if>
            <if test="channelType != null  and channelType != ''"> and channel_type = #{channelType}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectModelProviderById" parameterType="Long" resultMap="ModelProviderResult">
        <include refid="selectModelProviderVo"/>
        where id = #{id}
    </select>

    <insert id="insertModelProvider" parameterType="ai.showlab.system.domain.ModelProvider">
        insert into a_model_provider
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="providerName != null">provider_name,</if>
            <if test="providerKey != null">provider_key,</if>
            <if test="channelType != null">channel_type,</if>
            <if test="baseUrl != null">base_url,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="providerName != null">#{providerName},</if>
            <if test="providerKey != null">#{providerKey},</if>
            <if test="channelType != null">#{channelType},</if>
            <if test="baseUrl != null">#{baseUrl},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateModelProvider" parameterType="ai.showlab.system.domain.ModelProvider">
        update a_model_provider
        <trim prefix="SET" suffixOverrides=",">
            <if test="providerName != null">provider_name = #{providerName},</if>
            <if test="providerKey != null">provider_key = #{providerKey},</if>
            <if test="channelType != null">channel_type = #{channelType},</if>
            <if test="baseUrl != null">base_url = #{baseUrl},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteModelProviderById" parameterType="Long">
        delete from a_model_provider where id = #{id}
    </delete>

    <delete id="deleteModelProviderByIds" parameterType="String">
        delete from a_model_provider where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>