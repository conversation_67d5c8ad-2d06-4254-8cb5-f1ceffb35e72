<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.ModelOutputFormatMapper">
    
    <resultMap type="ai.showlab.system.domain.ModelOutputFormat" id="ModelOutputFormatResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="outputType"    column="output_type"    />
        <result property="supportsStream"    column="supports_stream"    />
        <result property="maxTokens"    column="max_tokens"    />
        <result property="description"    column="description"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectModelOutputFormatVo">
        select id, model_id, output_type, supports_stream, max_tokens, description, delete_time, create_by, update_by, create_time, update_time from a_model_output_format
    </sql>

    <select id="selectModelOutputFormatList" parameterType="ai.showlab.system.domain.ModelOutputFormat" resultMap="ModelOutputFormatResult">
        <include refid="selectModelOutputFormatVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="outputType != null "> and output_type = #{outputType}</if>
            <if test="supportsStream != null "> and supports_stream = #{supportsStream}</if>
            <if test="params.beginMaxTokens != null and params.beginMaxTokens != '' and params.endMaxTokens != null and params.endMaxTokens != ''">
                and max_tokens between CAST(#{params.beginMaxTokens} as timestamptz)
                and CAST(#{params.endMaxTokens} as timestamptz)
            </if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectModelOutputFormatById" parameterType="Long" resultMap="ModelOutputFormatResult">
        <include refid="selectModelOutputFormatVo"/>
        where id = #{id}
    </select>

    <insert id="insertModelOutputFormat" parameterType="ai.showlab.system.domain.ModelOutputFormat">
        insert into a_model_output_format
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="outputType != null">output_type,</if>
            <if test="supportsStream != null">supports_stream,</if>
            <if test="maxTokens != null">max_tokens,</if>
            <if test="description != null">description,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="outputType != null">#{outputType},</if>
            <if test="supportsStream != null">#{supportsStream},</if>
            <if test="maxTokens != null">#{maxTokens},</if>
            <if test="description != null">#{description},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateModelOutputFormat" parameterType="ai.showlab.system.domain.ModelOutputFormat">
        update a_model_output_format
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="outputType != null">output_type = #{outputType},</if>
            <if test="supportsStream != null">supports_stream = #{supportsStream},</if>
            <if test="maxTokens != null">max_tokens = #{maxTokens},</if>
            <if test="description != null">description = #{description},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteModelOutputFormatById" parameterType="Long">
        delete from a_model_output_format where id = #{id}
    </delete>

    <delete id="deleteModelOutputFormatByIds" parameterType="String">
        delete from a_model_output_format where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>