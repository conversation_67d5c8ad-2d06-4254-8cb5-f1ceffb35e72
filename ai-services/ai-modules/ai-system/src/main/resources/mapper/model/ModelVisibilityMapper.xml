<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.ModelVisibilityMapper">
    
    <resultMap type="ai.showlab.system.domain.ModelVisibility" id="ModelVisibilityResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="visibilityType"    column="visibility_type"    />
        <result property="referenceId"    column="reference_id"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="description"    column="description"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectModelVisibilityVo">
        select id, model_id, visibility_type, reference_id, is_enabled, description, delete_time, create_by, update_by, create_time, update_time from a_model_visibility
    </sql>

    <select id="selectModelVisibilityList" parameterType="ai.showlab.system.domain.ModelVisibility" resultMap="ModelVisibilityResult">
        <include refid="selectModelVisibilityVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="visibilityType != null "> and visibility_type = #{visibilityType}</if>
            <if test="referenceId != null "> and reference_id = #{referenceId}</if>
            <if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectModelVisibilityById" parameterType="Long" resultMap="ModelVisibilityResult">
        <include refid="selectModelVisibilityVo"/>
        where id = #{id}
    </select>

    <insert id="insertModelVisibility" parameterType="ai.showlab.system.domain.ModelVisibility">
        insert into a_model_visibility
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="visibilityType != null">visibility_type,</if>
            <if test="referenceId != null">reference_id,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="description != null">description,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="visibilityType != null">#{visibilityType},</if>
            <if test="referenceId != null">#{referenceId},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="description != null">#{description},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateModelVisibility" parameterType="ai.showlab.system.domain.ModelVisibility">
        update a_model_visibility
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="visibilityType != null">visibility_type = #{visibilityType},</if>
            <if test="referenceId != null">reference_id = #{referenceId},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="description != null">description = #{description},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteModelVisibilityById" parameterType="Long">
        delete from a_model_visibility where id = #{id}
    </delete>

    <delete id="deleteModelVisibilityByIds" parameterType="String">
        delete from a_model_visibility where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>