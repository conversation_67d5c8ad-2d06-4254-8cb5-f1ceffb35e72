<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>ai.showlab</groupId>
        <artifactId>ai-modules</artifactId>
        <version>1.0.0</version>
    </parent>
    <name>ai-system</name>
    <artifactId>ai-system</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencies>
        <!-- 停用 tomcat，改用 undertow -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>ai.showlab</groupId>
            <artifactId>ai-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>ai.showlab</groupId>
            <artifactId>ai-common-constants</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-modules-system</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-bootstrap</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>
    </dependencies>

    <build>
        <!--1. 名称-->
        <finalName>${project.artifactId}</finalName>
        <!--2. 项目资源路径-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <!--3. 测试资源路径-->
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
        <!--4. 打包插件引用-->
        <plugins>
            <!--4.1 maven打包插件-->
            <plugin>
                <!--4.1.1 maven打包插件版本引用-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
                <!--4.1.1 配置项-->
                <configuration>
                    <!--******* 配置要打成jar包的文件，此处只将class文件打成jar包-->
                    <includes>
                        <include>**/**.class</include>
                    </includes>
                    <archive>
                        <!-- 生成的jar中，包含pom.xml和pom.properties这两个文件 -->
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <!-- 为依赖包添加路径, 这些路径会写在MANIFEST文件的Class-Path下 -->
                            <addClasspath>true</addClasspath>
                            <!-- 这个jar所依赖的jar包添加classPath的时候的前缀，
                            如果这个jar本身和依赖包在同一级目录，则不需要添加 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- jar启动入口类 -->
                            <mainClass>ai.showlab.system.AiSystemApp</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!-- 在Class-Path下添加配置文件的路径 -->
                            <Class-Path>./</Class-Path>
                        </manifestEntries>
                    </archive>
                    <!-- 输出路径 -->
                    <outputDirectory>${project.build.directory}/release</outputDirectory>
                </configuration>
            </plugin>
            <!--4.2 maven打包插件,将依赖的jar打包至项目指定路径-->
            <plugin>
                <!--4.2.1 声明插件dependency引用-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <!--4.2.2 执行配置-->
                <executions>
                    <execution>
                        <id>copy</id>
                        <!--4.2.2.1 打包操作-->
                        <phase>package</phase>
                        <goals>
                            <!--4.2.2.1.1 目标：复制依赖jar-->
                            <goal>copy-dependencies</goal>
                        </goals>
                        <!--4.2.2.1 打包配置，配置jar输出路径-->
                        <configuration>
                            <outputDirectory>${project.build.directory}/release/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--4.3 maven打包插件,将依赖的项目配置文件打包至项目指定路径-->
            <plugin>
                <!--4.3.1 声明使用maven打包插件，分离配置文件插件-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <!--4.3.2 执行配置-->
                <executions>
                    <execution>
                        <!--4.3.2.1 id:复制配置文件-->
                        <id>copy-resources</id>
                        <!--4.3.2.2 打包操作-->
                        <phase>package</phase>
                        <goals>
                            <!--4.3.2.3 目标：复制配置文件-->
                            <goal>copy-resources</goal>
                        </goals>
                        <!--4.3.2.4 打包配置，配置class输出路径-->
                        <configuration>
                            <!--4.3.2.4.1 配置文件输出路径-->
                            <outputDirectory>${project.build.directory}/release</outputDirectory>
                            <!--4.3.2.4.2 声明配置文件引用路径-->
                            <resources>
                                <resource>
                                    <!--来源路径-->
                                    <directory>${basedir}/src/main/resources</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
