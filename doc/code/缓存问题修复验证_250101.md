# 缓存问题修复验证

## 问题描述

第一次登录时，Redis 中的 key `"bff:member:sessions:id::4"` 是空数组，说明缓存机制有问题。

## 根本原因

**调用顺序问题**：
1. 重复登录检查（调用 `getActiveSessionsForMember`）
2. 创建新会话记录

这导致在检查重复登录时，新的会话还没有创建，缓存中自然是空的。

## 修复方案

### 1. 创建会话后清除缓存

在 `createSessionForMember` 方法中，创建会话后立即清除相关缓存：

```java
// 4. 清除会话列表缓存，确保下次查询时获取最新数据
Cache sessionListCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY);
if (sessionListCache != null) {
    sessionListCache.evict(memberId);
    log.debug("创建会话后清除用户 {} 的会话列表缓存", memberId);
}
```

### 2. 缩短缓存TTL

将 `getActiveSessionsForMember` 的缓存TTL从默认的3600秒改为60秒：

```java
@CustomCache(value = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY, key = "#memberId", ttl = 60)
```

### 3. 增强调试日志

添加详细的调试日志来跟踪缓存和数据库查询的行为。

## 验证步骤

### 1. 清理环境

```sql
-- 清理测试用户的所有会话
DELETE FROM a_member_session WHERE member_id = 4;
```

```bash
# 清理Redis缓存
redis-cli del "bff:member:sessions:id::4"
redis-cli keys "bff:member:session:token::*" | xargs redis-cli del
```

### 2. 第一次登录测试

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**验证点**：

1. **数据库检查**：
   ```sql
   SELECT id, member_id, ip_address, is_active, login_time 
   FROM a_member_session 
   WHERE member_id = 4;
   ```
   期望：1条 `is_active=true` 的记录

2. **Redis检查**：
   ```bash
   redis-cli get "bff:member:sessions:id::4"
   ```
   期望：应该是空（因为缓存被清除了）或者包含新创建的会话

3. **日志检查**：
   ```
   用户 4 重复登录检查：当前活跃会话数量 0
   用户 4 首次登录，允许
   创建会话后清除用户 4 的会话列表缓存
   ```

### 3. 第二次登录测试

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**验证点**：

1. **数据库检查**：
   ```sql
   SELECT id, member_id, ip_address, is_active, login_time, create_time 
   FROM a_member_session 
   WHERE member_id = 4 
   ORDER BY create_time DESC;
   ```
   期望：2条记录，1条 `is_active=false`（被踢掉），1条 `is_active=true`（新登录）

2. **日志检查**：
   ```
   用户 4 重复登录检查：当前活跃会话数量 1
   活跃会话 - ID: 123, IP: 127.0.0.1, 登录时间: 2024-08-01T10:00:00
   用户 4 会话数量超限：当前 1 >= 最大 1, 执行策略 KICK_OLD
   用户 4 登录时踢掉了 1 个旧会话
   ```

### 4. 缓存行为验证

#### 测试缓存更新

```bash
# 第一次查询（应该从数据库查询）
redis-cli del "bff:member:sessions:id::4"

# 触发查询（通过登录或其他方式）
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{"identifier": "13800138000", "password": "aaBB@888"}'

# 检查缓存是否被设置
redis-cli exists "bff:member:sessions:id::4"
redis-cli ttl "bff:member:sessions:id::4"
```

期望：
- 缓存存在
- TTL约为60秒

#### 测试缓存清除

```bash
# 检查缓存存在
redis-cli exists "bff:member:sessions:id::4"

# 再次登录（应该清除缓存）
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{"identifier": "13800138000", "password": "aaBB@888"}'

# 检查缓存是否被清除
redis-cli exists "bff:member:sessions:id::4"
```

期望：缓存被清除（不存在）

## 调试技巧

### 1. 实时监控Redis

```bash
# 监控Redis操作
redis-cli monitor | grep "bff:member:sessions:id"
```

### 2. 数据库查询监控

```sql
-- 开启查询日志（PostgreSQL）
SET log_statement = 'all';
SET log_min_duration_statement = 0;

-- 查看当前连接的查询
SELECT query FROM pg_stat_activity WHERE state = 'active';
```

### 3. 应用日志级别

```yaml
# 在 application.yml 中设置
logging:
  level:
    ai.showlab.bff.service.v1.member.impl.MemberSessionServiceImpl: DEBUG
    ai.showlab.bff.service.v1.member.impl.DuplicateLoginServiceImpl: DEBUG
    ai.showlab.bff.common.aop.CustomCacheAspect: DEBUG
```

## 预期改进效果

### 修复前的问题

1. **第一次登录**：
   - 重复登录检查：查询到空数组（正确）
   - 创建会话：成功
   - 缓存状态：空数组被缓存

2. **第二次登录**：
   - 重复登录检查：从缓存获取空数组（错误！）
   - 判断：认为是首次登录
   - 结果：重复登录检测失效

### 修复后的行为

1. **第一次登录**：
   - 重复登录检查：查询到空数组（正确）
   - 创建会话：成功
   - 缓存清除：清除空数组缓存

2. **第二次登录**：
   - 重复登录检查：从数据库查询到1个活跃会话（正确！）
   - 判断：检测到重复登录
   - 执行策略：踢掉旧会话
   - 创建会话：成功
   - 缓存清除：清除旧缓存

## 性能考虑

### 缓存策略调整

1. **TTL缩短**：从3600秒改为60秒
   - 优点：数据更及时
   - 缺点：缓存命中率可能降低

2. **主动清除**：在数据变更时清除缓存
   - 优点：数据一致性好
   - 缺点：需要更多的缓存管理代码

### 监控指标

1. **缓存命中率**：监控 `bff:member:sessions:id` 的命中率变化
2. **数据库查询频率**：监控会话查询的频率
3. **重复登录检测准确性**：统计重复登录检测的成功率

## 总结

通过以上修复，重复登录功能应该能够：

1. **正确处理缓存**：创建会话后立即清除相关缓存
2. **及时获取数据**：通过缩短TTL确保数据及时性
3. **准确检测重复登录**：基于最新的会话数据进行判断
4. **详细的调试信息**：便于问题排查和监控
